#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Application - Start Now
تشغيل فوري للتطبيق

Quick start script that automatically generates secure keys and starts the application.
"""

import os
import sys
import secrets
from dotenv import load_dotenv

def generate_secure_env():
    """توليد ملف .env آمن"""
    print("[INFO] توليد مفاتيح أمان جديدة...")
    
    # قراءة الملف الحالي
    with open('.env', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # توليد مفاتيح آمنة جديدة
    jwt_secret = secrets.token_urlsafe(64)
    csrf_secret = secrets.token_urlsafe(64)
    master_key = secrets.token_urlsafe(64)
    flask_secret = secrets.token_urlsafe(64)
    
    # استبدال القيم غير الآمنة
    content = content.replace('your-jwt-secret-key-change-this-in-production', jwt_secret)
    content = content.replace('your-csrf-secret-key-change-this-in-production', csrf_secret)
    content = content.replace('your-master-encryption-key-change-this-in-production', master_key)
    content = content.replace('qr-viewer-secret-key-2024-firebase-enhanced', flask_secret)
    
    # حفظ الملف المحدث
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("[SUCCESS] تم توليد مفاتيح الأمان بنجاح")

def main():
    """تشغيل التطبيق فوراً"""
    try:
        print("=" * 60)
        print("QR Application - تشغيل فوري")
        print("=" * 60)
        
        # التحقق من وجود ملف .env
        if not os.path.exists('.env'):
            print("[ERROR] ملف .env غير موجود!")
            sys.exit(1)
        
        # توليد مفاتيح آمنة إذا لزم الأمر
        with open('.env', 'r', encoding='utf-8') as f:
            env_content = f.read()
            if 'your-' in env_content or 'change-this' in env_content:
                generate_secure_env()
        
        # تحميل متغيرات البيئة
        load_dotenv()
        
        print("[INFO] تحميل التطبيق...")
        
        # استيراد التطبيق
        try:
            from app import app, init_firebase
            print("[SUCCESS] تم تحميل التطبيق بنجاح")
        except ImportError as e:
            print(f"[ERROR] خطأ في استيراد التطبيق: {e}")
            print("تأكد من تثبيت المكتبات:")
            print("pip install -r requirements.txt")
            sys.exit(1)
        
        # تهيئة Firebase
        print("[INFO] تهيئة Firebase...")
        try:
            init_firebase()
            print("[SUCCESS] تم تهيئة Firebase بنجاح")
        except Exception as e:
            print(f"[WARNING] تحذير Firebase: {e}")
            print("[INFO] سيتم استخدام الملف المحلي كبديل")
        
        # إعداد السيرفر
        port = int(os.environ.get('PORT', 21226))
        debug = os.environ.get('FLASK_ENV') == 'development'
        
        print("\n" + "=" * 60)
        print("معلومات السيرفر")
        print("=" * 60)
        print(f"[SERVER] المنفذ: {port}")
        print(f"[SERVER] الرابط: http://localhost:{port}")
        print(f"[SERVER] وضع التطوير: {'مفعل' if debug else 'معطل'}")
        
        print("\n[SECURITY] الميزات الأمنية:")
        print("  ✓ تشفير البيانات الحساسة")
        print("  ✓ حماية من الهجمات الإلكترونية")
        print("  ✓ مراقبة أمنية في الوقت الفعلي")
        print("  ✓ Rate limiting متقدم")
        
        print("\n" + "=" * 60)
        print("بدء التشغيل...")
        print("=" * 60)
        print(f"[INFO] يمكنك الوصول للتطبيق عبر: http://localhost:{port}")
        print("[INFO] اضغط Ctrl+C لإيقاف السيرفر")
        print("")
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=port,
            debug=debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n[INFO] تم إيقاف السيرفر")
        print("[SUCCESS] تم إنهاء التطبيق بأمان")
    except Exception as e:
        print(f"\n[ERROR] خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
