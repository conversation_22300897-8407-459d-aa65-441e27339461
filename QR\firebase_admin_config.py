#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Firebase Admin SDK Configuration
إعدادات Firebase Admin SDK للجانب الخلفي

هذا الملف يحتوي على إعدادات Firebase Admin SDK للعمليات الإدارية
المتغيرات الحساسة محفوظة في ملف .env
"""

import os
import json
import logging
from typing import Optional, Dict, Any
import firebase_admin
from firebase_admin import credentials, firestore, auth, storage
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logger = logging.getLogger(__name__)

class FirebaseAdminManager:
    """مدير Firebase Admin SDK"""
    
    def __init__(self):
        self.app: Optional[firebase_admin.App] = None
        self.db: Optional[firestore.Client] = None
        self.auth_client: Optional[auth.Client] = None
        self.storage_client: Optional[storage.bucket] = None
        self._initialized = False
    
    def initialize(self) -> bool:
        """تهيئة Firebase Admin SDK"""
        try:
            if self._initialized:
                logger.info("Firebase Admin SDK already initialized")
                return True
            
            # إنشاء credentials من متغيرات البيئة
            cred_dict = self._get_credentials_dict()
            
            if not cred_dict:
                logger.error("Failed to load Firebase credentials")
                return False
            
            # تهيئة Firebase Admin
            cred = credentials.Certificate(cred_dict)
            self.app = firebase_admin.initialize_app(cred, {
                'databaseURL': 'https://car-report-a01fe-default-rtdb.firebaseio.com',
                'storageBucket': 'car-report-a01fe.firebasestorage.app'
            })
            
            # تهيئة الخدمات
            self.db = firestore.client()
            self.auth_client = auth
            self.storage_client = storage.bucket()
            
            self._initialized = True
            logger.info("Firebase Admin SDK initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Firebase Admin SDK: {e}")
            return False
    
    def _get_credentials_dict(self) -> Optional[Dict[str, Any]]:
        """إنشاء قاموس credentials من متغيرات البيئة"""
        try:
            # التحقق من وجود المتغيرات المطلوبة
            required_vars = [
                'FIREBASE_TYPE',
                'FIREBASE_PROJECT_ID',
                'FIREBASE_PRIVATE_KEY_ID',
                'FIREBASE_PRIVATE_KEY',
                'FIREBASE_CLIENT_EMAIL',
                'FIREBASE_CLIENT_ID',
                'FIREBASE_AUTH_URI',
                'FIREBASE_TOKEN_URI',
                'FIREBASE_AUTH_PROVIDER_X509_CERT_URL',
                'FIREBASE_CLIENT_X509_CERT_URL'
            ]
            
            for var in required_vars:
                if not os.getenv(var):
                    logger.error(f"Missing required environment variable: {var}")
                    return None
            
            # إنشاء قاموس credentials
            cred_dict = {
                "type": os.getenv('FIREBASE_TYPE'),
                "project_id": os.getenv('FIREBASE_PROJECT_ID'),
                "private_key_id": os.getenv('FIREBASE_PRIVATE_KEY_ID'),
                "private_key": os.getenv('FIREBASE_PRIVATE_KEY').replace('\\n', '\n'),
                "client_email": os.getenv('FIREBASE_CLIENT_EMAIL'),
                "client_id": os.getenv('FIREBASE_CLIENT_ID'),
                "auth_uri": os.getenv('FIREBASE_AUTH_URI'),
                "token_uri": os.getenv('FIREBASE_TOKEN_URI'),
                "auth_provider_x509_cert_url": os.getenv('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
                "client_x509_cert_url": os.getenv('FIREBASE_CLIENT_X509_CERT_URL'),
                "universe_domain": os.getenv('FIREBASE_UNIVERSE_DOMAIN', 'googleapis.com')
            }
            
            return cred_dict
            
        except Exception as e:
            logger.error(f"Error creating credentials dict: {e}")
            return None
    
    def get_firestore_client(self) -> Optional[firestore.Client]:
        """الحصول على عميل Firestore"""
        if not self._initialized:
            self.initialize()
        return self.db
    
    def get_auth_client(self):
        """الحصول على عميل المصادقة"""
        if not self._initialized:
            self.initialize()
        return self.auth_client
    
    def get_storage_client(self):
        """الحصول على عميل التخزين"""
        if not self._initialized:
            self.initialize()
        return self.storage_client
    
    def verify_user_token(self, id_token: str) -> Optional[Dict[str, Any]]:
        """التحقق من رمز المستخدم"""
        try:
            if not self._initialized:
                self.initialize()
            
            decoded_token = self.auth_client.verify_id_token(id_token)
            return decoded_token
            
        except Exception as e:
            logger.error(f"Error verifying user token: {e}")
            return None
    
    def create_custom_token(self, uid: str, additional_claims: Optional[Dict] = None) -> Optional[str]:
        """إنشاء رمز مخصص للمستخدم"""
        try:
            if not self._initialized:
                self.initialize()
            
            custom_token = self.auth_client.create_custom_token(uid, additional_claims)
            return custom_token.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating custom token: {e}")
            return None

# إنشاء مثيل عام
firebase_manager = FirebaseAdminManager()

# دوال مساعدة للاستخدام السريع
def get_firestore_client():
    """الحصول على عميل Firestore"""
    return firebase_manager.get_firestore_client()

def get_auth_client():
    """الحصول على عميل المصادقة"""
    return firebase_manager.get_auth_client()

def get_storage_client():
    """الحصول على عميل التخزين"""
    return firebase_manager.get_storage_client()

def verify_token(id_token: str):
    """التحقق من رمز المستخدم"""
    return firebase_manager.verify_user_token(id_token)

def create_token(uid: str, claims: Optional[Dict] = None):
    """إنشاء رمز مخصص"""
    return firebase_manager.create_custom_token(uid, claims)

# تهيئة تلقائية عند استيراد الملف
if __name__ != '__main__':
    firebase_manager.initialize()
