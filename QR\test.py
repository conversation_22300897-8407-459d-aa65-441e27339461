#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Contract Viewer - Test Suite
===============================

اختبارات للتأكد من عمل التطبيق بشكل صحيح
"""

import unittest
import os
import sys
from unittest.mock import patch, MagicMock

# إضافة مجلد التطبيق إلى المسار
sys.path.insert(0, os.path.dirname(__file__))

class TestQRViewer(unittest.TestCase):
    """اختبارات التطبيق الأساسية"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # تعيين متغيرات البيئة للاختبار
        os.environ['FIREBASE_PROJECT_ID'] = 'test-project'
        os.environ['FIREBASE_PRIVATE_KEY'] = 'test-key'
        os.environ['FIREBASE_CLIENT_EMAIL'] = '<EMAIL>'
        os.environ['FIREBASE_DATABASE_URL'] = 'https://test.firebaseio.com'
        os.environ['SECRET_KEY'] = 'test-secret-key'
        
    def test_uuid_validation(self):
        """اختبار التحقق من صحة UUID"""
        from app import validate_uuid
        
        # UUID صحيح
        valid_uuid = "12345678-1234-1234-1234-123456789abc"
        self.assertTrue(validate_uuid(valid_uuid))
        
        # UUID غير صحيح
        invalid_uuid = "invalid-uuid"
        self.assertFalse(validate_uuid(invalid_uuid))
        
        # UUID فارغ
        self.assertFalse(validate_uuid(""))
        
        # UUID بأحرف كبيرة
        upper_uuid = "12345678-1234-1234-1234-123456789ABC"
        self.assertTrue(validate_uuid(upper_uuid))
    
    @patch('firebase_admin.initialize_app')
    @patch('firebase_admin.credentials.Certificate')
    def test_app_creation(self, mock_cert, mock_init):
        """اختبار إنشاء التطبيق"""
        from app import app
        
        # التأكد من إنشاء التطبيق
        self.assertIsNotNone(app)
        self.assertEqual(app.config['SECRET_KEY'], 'test-secret-key')
    
    @patch('firebase_admin.initialize_app')
    @patch('firebase_admin.credentials.Certificate')
    def test_health_endpoint(self, mock_cert, mock_init):
        """اختبار endpoint فحص الصحة"""
        from app import app
        
        with app.test_client() as client:
            response = client.get('/health')
            self.assertEqual(response.status_code, 200)
            
            data = response.get_json()
            self.assertIn('status', data)
            self.assertEqual(data['status'], 'healthy')
    
    @patch('firebase_admin.initialize_app')
    @patch('firebase_admin.credentials.Certificate')
    def test_index_page(self, mock_cert, mock_init):
        """اختبار الصفحة الرئيسية"""
        from app import app
        
        with app.test_client() as client:
            response = client.get('/')
            self.assertEqual(response.status_code, 200)
            self.assertIn('text/html', response.content_type)
    
    @patch('firebase_admin.initialize_app')
    @patch('firebase_admin.credentials.Certificate')
    def test_invalid_contract_uuid(self, mock_cert, mock_init):
        """اختبار UUID غير صحيح للعقد"""
        from app import app
        
        with app.test_client() as client:
            response = client.get('/contract/invalid-uuid')
            self.assertEqual(response.status_code, 404)
    
    @patch('firebase_admin.initialize_app')
    @patch('firebase_admin.credentials.Certificate')
    @patch('app.get_contract_from_firebase')
    def test_contract_not_found(self, mock_get_contract, mock_cert, mock_init):
        """اختبار عقد غير موجود"""
        from app import app
        
        # محاكاة عدم وجود العقد
        mock_get_contract.return_value = None
        
        with app.test_client() as client:
            valid_uuid = "12345678-1234-1234-1234-123456789abc"
            response = client.get(f'/contract/{valid_uuid}')
            self.assertEqual(response.status_code, 404)
    
    @patch('firebase_admin.initialize_app')
    @patch('firebase_admin.credentials.Certificate')
    @patch('app.get_contract_from_firebase')
    def test_contract_found(self, mock_get_contract, mock_cert, mock_init):
        """اختبار عقد موجود"""
        from app import app
        
        # محاكاة وجود العقد
        mock_contract = {
            'uuid': '12345678-1234-1234-1234-123456789abc',
            'serial_number': '123',
            'seller_name': 'البائع',
            'buyer_name': 'المشتري',
            'sale_amount': 1000,
            'currency': 'USD'
        }
        mock_get_contract.return_value = mock_contract
        
        with app.test_client() as client:
            valid_uuid = "12345678-1234-1234-1234-123456789abc"
            response = client.get(f'/contract/{valid_uuid}')
            self.assertEqual(response.status_code, 200)
            self.assertIn('text/html', response.content_type)

class TestSecurityFeatures(unittest.TestCase):
    """اختبارات الأمان"""
    
    def setUp(self):
        """إعداد اختبارات الأمان"""
        os.environ['FIREBASE_PROJECT_ID'] = 'test-project'
        os.environ['FIREBASE_PRIVATE_KEY'] = 'test-key'
        os.environ['FIREBASE_CLIENT_EMAIL'] = '<EMAIL>'
        os.environ['FIREBASE_DATABASE_URL'] = 'https://test.firebaseio.com'
        os.environ['SECRET_KEY'] = 'test-secret-key'
    
    @patch('firebase_admin.initialize_app')
    @patch('firebase_admin.credentials.Certificate')
    def test_security_headers(self, mock_cert, mock_init):
        """اختبار headers الأمان"""
        from app import app
        
        with app.test_client() as client:
            response = client.get('/')
            
            # التحقق من وجود headers الأمان
            self.assertIn('X-Content-Type-Options', response.headers)
            self.assertIn('X-Frame-Options', response.headers)
            self.assertIn('X-XSS-Protection', response.headers)
            self.assertIn('Content-Security-Policy', response.headers)
            
            # التحقق من قيم headers
            self.assertEqual(response.headers['X-Content-Type-Options'], 'nosniff')
            self.assertEqual(response.headers['X-Frame-Options'], 'DENY')

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 تشغيل اختبارات QR Contract Viewer")
    print("===================================")
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2, exit=False)

if __name__ == '__main__':
    run_tests()
