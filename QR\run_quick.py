#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Application Quick Start
تشغيل سريع للتطبيق
"""

import os
import sys
import secrets
from dotenv import load_dotenv

def update_env_file():
    """تحديث ملف .env بمفاتيح آمنة"""
    try:
        # قراءة الملف الحالي
        with open('.env', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # توليد مفاتيح آمنة
        jwt_secret = secrets.token_urlsafe(64)
        csrf_secret = secrets.token_urlsafe(64)
        master_key = secrets.token_urlsafe(64)
        flask_secret = secrets.token_urlsafe(64)
        
        # استبدال القيم غير الآمنة
        content = content.replace('your-jwt-secret-key-change-this-in-production', jwt_secret)
        content = content.replace('your-csrf-secret-key-change-this-in-production', csrf_secret)
        content = content.replace('your-master-encryption-key-change-this-in-production', master_key)
        content = content.replace('qr-viewer-secret-key-2024-firebase-enhanced', flask_secret)
        
        # حفظ الملف
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("تم توليد مفاتيح الأمان")
        return True
    except Exception as e:
        print(f"خطأ في تحديث ملف .env: {e}")
        return False

def main():
    """تشغيل التطبيق"""
    try:
        print("QR Application - تشغيل سريع")
        print("=" * 40)
        
        # التحقق من ملف .env
        if not os.path.exists('.env'):
            print("خطأ: ملف .env غير موجود")
            return
        
        # تحديث المفاتيح إذا لزم الأمر
        with open('.env', 'r', encoding='utf-8') as f:
            if 'your-' in f.read():
                print("توليد مفاتيح أمان جديدة...")
                update_env_file()
        
        # تحميل متغيرات البيئة
        load_dotenv()
        
        print("تحميل التطبيق...")
        
        # استيراد التطبيق
        from app import app, init_firebase
        
        print("تهيئة Firebase...")
        try:
            init_firebase()
            print("تم تهيئة Firebase بنجاح")
        except Exception as e:
            print(f"تحذير Firebase: {e}")
            print("سيتم استخدام الملف المحلي")
        
        # إعداد السيرفر
        port = int(os.environ.get('PORT', 21226))
        
        print(f"\nتشغيل السيرفر على المنفذ: {port}")
        print(f"الرابط: http://localhost:{port}")
        print("\nالميزات الأمنية مفعلة:")
        print("- تشفير البيانات الحساسة")
        print("- حماية من الهجمات")
        print("- مراقبة أمنية")
        print("- Rate limiting")
        print("\nاضغط Ctrl+C لإيقاف السيرفر")
        print("-" * 40)
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\nتم إيقاف السيرفر")
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
