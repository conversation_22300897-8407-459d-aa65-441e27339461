# تحديثات واجهة عرض العقود QR

## التحديثات المضافة

### 1. الحقول الجديدة المضافة

#### بيانات الأطراف
- **المالك الشرعي**: اسم وعنوان المالك الشرعي
- **بيانات البائع الكاملة**: الاسم، العنوان، رقم الهوية، رقم الهاتف
- **بيانات المشتري الكاملة**: الاسم، العنوان، رقم الهوية، رقم الهاتف

#### بيانات السيارة المفصلة
- **لون السيارة**: عرض لون السيارة
- **رقم السيارة**: رقم لوحة السيارة
- **المدينة**: مدينة تسجيل السيارة
- **نوع السيارة**: خصوصي/أجرة/حكومي
- **رقم السنوية**: رقم السنوية للسيارة

#### بيانات التوقيت المفصلة
- **الفترة**: صباحاً/مساءً
- **اليوم**: يوم الأسبوع

#### الملاحظات
- **ملاحظة أ**: ملاحظات إضافية
- **ملاحظة ب**: ملاحظات إضافية

### 2. تحسينات التصميم

#### تخطيط الأطراف
- تم تغيير التخطيط من عمودين إلى ثلاثة أعمدة لعرض:
  - المالك الشرعي
  - البائع
  - المشتري
- إضافة عرض تفاصيل كاملة لكل طرف (العنوان، رقم الهوية، الهاتف)

#### التصميم المتجاوب
- دعم الشاشات الكبيرة (3 أعمدة)
- دعم الشاشات المتوسطة (عمودين)
- دعم الشاشات الصغيرة (عمود واحد)

### 3. التوافق مع النماذج القديمة

تم إضافة دعم كامل للحقول القديمة والجديدة:

#### أسماء الحقول القديمة → الجديدة
```
name_1 → legal_owner_name (المالك الشرعي)
name_2 → seller_name (البائع)
name_3 → buyer_name (المشتري)
location_1 → legal_owner_address
location_2 → seller_address
location_3 → buyer_address
id_1 → seller_id
id_2 → buyer_id
phone_1 → seller_phone
phone_2 → buyer_phone
car_type → car_make
car_colar → car_color
car_num → car_number
car_prop → car_property
sin_num → annual_number
sasi_num → chassis_number
badal_num → sale_amount
mony_num → paid_amount
mony_not_delevired → remaining_amount
currency_type → currency
t → time
t_1 → period
```

### 4. تحسينات البيانات المالية

- دعم عرض المبالغ بالتنسيق الصحيح
- إزالة الفواصل من الأرقام قبل التحويل
- دعم العملات المختلفة (USD/IQD)
- عرض النص العربي للمبالغ

### 5. ملف الاختبار

تم إنشاء `test_complete_contract.py` لإنشاء عقد تجريبي يحتوي على جميع الحقول المتاحة لاختبار الواجهة.

## كيفية الاستخدام

### تشغيل خادم QR
```bash
cd QR
python app.py
```

### إنشاء عقد تجريبي كامل
```bash
cd QR
python test_complete_contract.py
```

### الوصول للعقد
- محلياً: `http://localhost:21226/contract/[UUID]`
- عبر الإنترنت: `https://425243.site.bot-hosting.net/contract/[UUID]`

## الملفات المحدثة

1. **QR/templates/contract.html**: قالب عرض العقد مع جميع الحقول الجديدة
2. **QR/app.py**: دالة `normalize_contract_data` محدثة لدعم جميع الحقول
3. **QR/test_complete_contract.py**: ملف اختبار جديد لإنشاء عقد كامل

## المميزات الجديدة

- ✅ عرض جميع بيانات الأطراف (المالك الشرعي، البائع، المشتري)
- ✅ عرض تفاصيل السيارة الكاملة
- ✅ عرض بيانات التوقيت المفصلة
- ✅ عرض الملاحظات
- ✅ التوافق مع النماذج القديمة والجديدة
- ✅ تصميم متجاوب لجميع أحجام الشاشات
- ✅ معالجة محسنة للبيانات المالية

## ملاحظات مهمة

- جميع التحديثات متوافقة مع النظام الحالي
- لا تحتاج لتغيير بنية البيانات في Firebase
- يدعم العقود القديمة والجديدة بنفس الواجهة
- تم اختبار جميع الحقول والتأكد من عملها
