#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Application Secure Runner
تشغيل تطبيق QR الآمن

This script runs the QR application with maximum security features enabled.
يقوم هذا الملف بتشغيل تطبيق QR مع تفعيل جميع الميزات الأمنية.
"""

import os
import sys
import secrets
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_security_requirements():
    """فحص متطلبات الأمان"""
    print("🔒 فحص متطلبات الأمان...")
    
    # فحص متغيرات البيئة الأمنية
    required_vars = [
        'FLASK_SECRET_KEY',
        'JWT_SECRET_KEY', 
        'CSRF_SECRET_KEY',
        'MASTER_ENCRYPTION_KEY'
    ]
    
    missing_vars = []
    weak_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        elif value.startswith('your-') or len(value) < 32:
            weak_vars.append(var)
    
    if missing_vars or weak_vars:
        print("❌ مشاكل في الإعدادات الأمنية:")
        
        if missing_vars:
            print("   متغيرات مفقودة:")
            for var in missing_vars:
                print(f"     - {var}")
        
        if weak_vars:
            print("   متغيرات ضعيفة (يجب تغييرها):")
            for var in weak_vars:
                print(f"     - {var}")
        
        print("\n🔑 قيم آمنة مقترحة:")
        for var in missing_vars + weak_vars:
            secure_value = secrets.token_urlsafe(64)
            print(f"   {var}={secure_value}")
        
        print("\n⚠️ يرجى تحديث ملف .env بالقيم الآمنة أعلاه")
        return False
    
    print("✅ جميع متطلبات الأمان متوفرة")
    return True

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    print("📦 فحص المكتبات المطلوبة...")
    
    required_modules = [
        'flask',
        'cryptography',
        'jwt',
        'firebase_admin'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ المكتبات التالية مفقودة:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n📝 لتثبيت المكتبات المفقودة:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ جميع المكتبات المطلوبة متوفرة")
    return True

def display_security_status():
    """عرض حالة الأمان"""
    print("\n🛡️ حالة الأمان:")
    print("="*50)
    
    security_features = [
        "✅ تشفير AES-256 للبيانات الحساسة",
        "✅ مصادقة JWT مع حماية CSRF", 
        "✅ حماية من هجمات SQL Injection",
        "✅ حماية من هجمات XSS",
        "✅ حماية من هجمات Command Injection",
        "✅ Rate limiting متقدم",
        "✅ مراقبة أمنية في الوقت الفعلي",
        "✅ حظر تلقائي للـ IPs المشبوهة",
        "✅ Security headers متقدمة",
        "✅ فرض HTTPS",
        "✅ تسجيل شامل للأحداث الأمنية"
    ]
    
    for feature in security_features:
        print(f"   {feature}")
    
    print("="*50)

def main():
    """تشغيل التطبيق الآمن"""
    try:
        print("🔒 QR Application - Secure Mode")
        print("="*40)
        
        # التحقق من وجود ملف .env
        if not os.path.exists('.env'):
            print("❌ ملف .env غير موجود!")
            print("📝 يرجى نسخ .env.example إلى .env وتعديل القيم")
            print("   cp .env.example .env")
            sys.exit(1)
        
        # فحص المكتبات
        if not check_dependencies():
            sys.exit(1)
        
        # فحص متطلبات الأمان
        if not check_security_requirements():
            sys.exit(1)
        
        # عرض حالة الأمان
        display_security_status()
        
        # استيراد التطبيق بعد التحقق من المتطلبات
        try:
            from app import app, init_firebase
        except ImportError as e:
            print(f"❌ خطأ في استيراد التطبيق: {e}")
            sys.exit(1)
        
        # تهيئة Firebase
        print("\n🔥 تهيئة Firebase...")
        try:
            init_firebase()
            print("✅ تم تهيئة Firebase بنجاح")
        except Exception as e:
            print(f"⚠️ تحذير Firebase: {e}")
            print("📝 سيتم استخدام الملف المحلي كبديل")
        
        # إعداد السيرفر الآمن
        port = int(os.environ.get('PORT', 21226))
        debug = os.environ.get('FLASK_ENV') == 'development'
        https_only = os.environ.get('HTTPS_ONLY', 'true').lower() == 'true'
        
        print(f"\n🚀 تشغيل السيرفر الآمن على المنفذ {port}")
        
        if https_only and not debug:
            print(f"🔒 الرابط الآمن: https://localhost:{port}")
            print("🛡️ HTTPS إجباري - جميع طلبات HTTP سيتم إعادة توجيهها")
        else:
            print(f"🌐 الرابط: http://localhost:{port}")
            if not https_only:
                print("⚠️ تحذير: HTTPS غير مفعل (للتطوير فقط)")
        
        print(f"🔧 وضع التطوير: {'مفعل' if debug else 'معطل'}")
        
        print("\n📊 للوصول إلى إحصائيات الأمان:")
        print(f"   {('https' if https_only and not debug else 'http')}://localhost:{port}/api/security/stats")
        
        print("\n🔍 ملفات السجلات:")
        print("   - security.log: سجل الأحداث العامة")
        print("   - security_events.log: سجل الأحداث الأمنية")
        
        print("\n⚡ بدء التشغيل...")
        
        # تشغيل التطبيق
        if https_only and not debug:
            # في الإنتاج مع HTTPS
            try:
                app.run(
                    host='0.0.0.0',
                    port=port,
                    debug=False,
                    threaded=True,
                    ssl_context='adhoc'  # للتطوير - استخدم شهادات حقيقية في الإنتاج
                )
            except Exception as e:
                print(f"⚠️ فشل تشغيل HTTPS، التبديل إلى HTTP: {e}")
                app.run(
                    host='0.0.0.0',
                    port=port,
                    debug=False,
                    threaded=True
                )
        else:
            # للتطوير
            app.run(
                host='0.0.0.0',
                port=port,
                debug=debug,
                threaded=True
            )
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف السيرفر الآمن")
        print("🔒 جميع الجلسات الأمنية تم إنهاؤها بأمان")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        print("📝 يرجى مراجعة سجلات الأخطاء للمزيد من التفاصيل")
        sys.exit(1)

if __name__ == '__main__':
    main()
