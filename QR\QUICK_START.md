# QR Contract Viewer - د<PERSON>يل البدء السريع 🚀

## التثبيت السريع

### Windows:
```cmd
install.bat
```

### Linux/Mac:
```bash
./install.sh
```

## الإعداد

1. **نسخ ملف البيئة:**
```bash
cp .env.example .env
```

2. **تعديل ملف .env بقيم Firebase الصحيحة**

3. **فحص الإعدادات:**
```bash
python check.py
```

## التشغيل

### تشغيل سريع:
```bash
python run.py
```

### تشغيل للتطوير:
```bash
./start.sh dev        # Linux/Mac
start.bat dev         # Windows
```

### تشغيل للإنتاج:
```bash
./start.sh gunicorn   # Linux/Mac
gunicorn wsgi:application --bind 0.0.0.0:21226 --workers 4
```

## الاختبار

```bash
python test.py
```

## الروابط المهمة

- **الصفحة الرئيسية:** http://localhost:21226/
- **فحص الصحة:** http://localhost:21226/health
- **عرض عقد:** http://localhost:21226/contract/UUID

## المشاكل الشائعة

### خطأ في Firebase:
- تحقق من ملف .env
- تأكد من صحة Service Account

### خطأ في المنفذ:
```bash
export PORT=8080  # تغيير المنفذ
```

### خطأ في المتطلبات:
```bash
pip install -r requirements.txt
```

## الأمان

- ✅ Rate limiting (100 طلب/دقيقة)
- ✅ UUID validation
- ✅ Security headers
- ✅ Input validation
- ✅ Firebase integration
- ✅ Copy protection

## الدعم

للمساعدة:
1. راجع ملف README.md
2. تشغيل `python check.py`
3. فحص السجلات
