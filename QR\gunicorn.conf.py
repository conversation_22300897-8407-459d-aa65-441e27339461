# إعدادات Gunicorn محسنة لتطبيق QR على السيرفر البسيط
# تهدف لتقليل استهلاك CPU والذاكرة

import os

# إعدادات الخادم الأساسية
bind = "0.0.0.0:21226"
backlog = 256

# إعدادات العمال - محسنة للسيرفر البسيط
# استخدام عامل واحد فقط لتطبيق QR البسيط
workers = 1
worker_class = "sync"
worker_connections = 50  # تقليل عدد الاتصالات لتطبيق QR
max_requests = 300  # إعادة تشغيل العامل بعد 300 طلب
max_requests_jitter = 30

# إعدادات المهلة الزمنية - محسنة للأداء
timeout = 30  # timeout أقصر لتطبيق QR
keepalive = 2
graceful_timeout = 15

# إعدادات الذاكرة والأداء
preload_app = True
max_worker_memory = 200  # حد أقصى 200 ميجابايت لتطبيق QR

# إعدادات التسجيل - تقليل التسجيل
loglevel = "error"  # الأخطاء فقط
accesslog = None
errorlog = "-"

# إعدادات الأمان
limit_request_line = 1024
limit_request_fields = 30
limit_request_field_size = 2048

# إعدادات إضافية
enable_stdio_inheritance = True
reuse_port = True

def when_ready(server):
    """يتم استدعاؤها عند جاهزية خادم QR"""
    server.log.info("🚀 خادم QR جاهز مع إعدادات محسنة")

def post_fork(server, worker):
    """تحسينات بعد إنشاء عامل QR"""
    server.log.info(f"✅ تم إنشاء عامل QR: {worker.pid}")
    
    try:
        import gc
        gc.set_threshold(500, 5, 5)  # تحسين garbage collection لتطبيق QR
    except Exception as e:
        server.log.warning(f"⚠️ فشل في تحسين QR garbage collection: {e}")

# إعدادات البيئة
raw_env = [
    'PYTHONUNBUFFERED=1',
    'PYTHONOPTIMIZE=1',
]

forwarded_allow_ips = '*'
