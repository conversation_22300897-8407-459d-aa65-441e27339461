[Unit]
Description=QR Contract Viewer
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/qr-viewer
Environment=PATH=/opt/qr-viewer/venv/bin
ExecStart=/opt/qr-viewer/venv/bin/gunicorn wsgi:application --bind 0.0.0.0:21226 --workers 4 --timeout 120
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/qr-viewer
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
