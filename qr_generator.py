#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Code Generator for Contracts
===============================

هذا السكريبت يولد QR Code للعقود ويحفظها كصور PNG.
الباركود يشير إلى موقع خارجي منفصل لعرض العقود.

ملاحظة مهمة:
- الموقع المحدد في DOMAIN هو موقع منفصل تماماً عن الموقع الحالي
- يستخدم فقط لعرض العقود عند قراءة QR Code
- يمكن استضافته على سيرفر خارجي مختلف
- لا يحتوي على أي فهرسة أو كشف لمسارات العقود
"""

import uuid
import qrcode
import os
from typing import Dict, Any

# إعدادات المجال - يمكن تغييره بسهولة
DOMAIN = "https://425243.site.bot-hosting.net"

# إعدادات المجلدات
QR_CODES_DIR = "qrcodes"

def ensure_directories():
    """إنشاء المجلدات المطلوبة إذا لم تكن موجودة"""
    os.makedirs(QR_CODES_DIR, exist_ok=True)

def generate_contract_qr(contract_uuid: str) -> str:
    """
    توليد QR Code للعقد
    
    Args:
        contract_uuid (str): UUID الخاص بالعقد
        
    Returns:
        str: مسار ملف الصورة المحفوظة
    """
    ensure_directories()
    
    # إنشاء الرابط الكامل
    contract_url = f"{DOMAIN}/contract/{contract_uuid}"
    
    # إنشاء QR Code
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(contract_url)
    qr.make(fit=True)
    
    # إنشاء الصورة
    img = qr.make_image(fill_color="black", back_color="white")
    
    # حفظ الصورة
    qr_filename = f"{contract_uuid}.png"
    qr_path = os.path.join(QR_CODES_DIR, qr_filename)
    img.save(qr_path)
    
    print(f"تم إنشاء QR Code: {qr_path}")
    print(f"الرابط: {contract_url}")
    
    return qr_path

def generate_new_contract_qr(data_dict: Dict[str, Any]) -> tuple:
    """
    توليد UUID جديد و QR Code للعقد
    
    Args:
        data_dict (Dict[str, Any]): بيانات العقد (للمرجع فقط، لا تستخدم في QR)
        
    Returns:
        tuple: (contract_uuid, qr_path)
    """
    # توليد UUID فريد
    contract_uuid = str(uuid.uuid4())
    
    # توليد QR Code
    qr_path = generate_contract_qr(contract_uuid)
    
    return contract_uuid, qr_path

# مثال للاستخدام
if __name__ == "__main__":
    # بيانات العقد التجريبية
    sample_contract = {
        "seller_name": "أحمد محمد",
        "buyer_name": "علي حسن", 
        "amount": "5000000",
        "date": "2024-01-15",
        "details": "بيع سيارة تويوتا كامري 2020"
    }
    
    # توليد QR Code جديد
    contract_id, qr_file = generate_new_contract_qr(sample_contract)
    
    print(f"\nتم إنشاء عقد جديد:")
    print(f"UUID: {contract_id}")
    print(f"QR Code: {qr_file}")
    print(f"سيتم عرض العقد على: {DOMAIN}/contract/{contract_id}")
