"""
Security Module for QR Application
وحدة الأمان لتطبيق QR

This module provides comprehensive security features including:
- Advanced encryption (AES-256)
- JWT-based authentication
- CSRF protection
- API rate limiting
- Input validation and sanitization
- Anomaly detection

يوفر هذا الملف ميزات أمان شاملة تشمل:
- التشفير المتقدم
- المصادقة باستخدام JWT
- حماية CSRF
- تحديد معدل API
- التحقق من صحة المدخلات وتنظيفها
- اكتشاف الشذوذ
"""

from .encryption import (
    encryption_manager,
    encrypt_sensitive_data,
    decrypt_sensitive_data,
    hash_password,
    verify_password
)

from .auth import (
    auth_manager,
    require_auth,
    require_csrf,
    require_permission
)

from .api_protection import (
    api_protection,
    rate_limit,
    validate_json_input,
    sanitize_form_input,
    block_suspicious_requests
)

from .monitoring import (
    security_monitor,
    log_security_event,
    detect_attack_patterns,
    analyze_request_anomaly,
    is_ip_blocked,
    get_security_stats
)

__all__ = [
    # Encryption
    'encryption_manager',
    'encrypt_sensitive_data',
    'decrypt_sensitive_data',
    'hash_password',
    'verify_password',

    # Authentication
    'auth_manager',
    'require_auth',
    'require_csrf',
    'require_permission',

    # API Protection
    'api_protection',
    'rate_limit',
    'validate_json_input',
    'sanitize_form_input',
    'block_suspicious_requests',

    # Security Monitoring
    'security_monitor',
    'log_security_event',
    'detect_attack_patterns',
    'analyze_request_anomaly',
    'is_ip_blocked',
    'get_security_stats'
]
