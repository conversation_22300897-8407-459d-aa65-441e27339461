{% extends "base.html" %}

{% block title %}عقد رقم {{ contract.serial_number }}{% endblock %}

{% block header_title %}الباركود الصادر من مكتب دبي{% endblock %}
{% block header_subtitle %}عقد رقم {{ contract.serial_number }}{% endblock %}

{% block extra_css %}
<style>
    .contract-details {
        background: #f8f9fa;
        padding: 30px;
        border-radius: 10px;
        margin: 20px 0;
        border-right: 5px solid #3498db;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #ecf0f1;
    }
    
    .detail-row:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #2c3e50;
        min-width: 150px;
    }
    
    .detail-value {
        color: #34495e;
        font-size: 1.1em;
        flex: 1;
        text-align: left;
        direction: ltr;
    }
    
    .detail-value.arabic {
        text-align: right;
        direction: rtl;
    }
    
    .amount-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 10px;
        margin: 20px 0;
        text-align: center;
    }
    
    .amount-title {
        font-size: 1.2em;
        margin-bottom: 10px;
        opacity: 0.9;
    }
    
    .amount-value {
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .amount-text {
        font-size: 1.1em;
        opacity: 0.9;
        font-style: italic;
    }
    
    .contract-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
        border-radius: 10px;
    }
    
    .contract-title {
        font-size: 2.5em;
        margin-bottom: 10px;
        font-weight: 300;
    }
    
    .contract-subtitle {
        font-size: 1.2em;
        opacity: 0.9;
    }
    
    .parties-section {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        margin: 30px 0;
    }
    
    .party-card {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
    }

    .party-address, .party-id, .party-phone {
        font-size: 0.9em;
        color: #666;
        margin-top: 5px;
    }
    
    .party-title {
        font-size: 1.3em;
        color: #2c3e50;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .party-name {
        font-size: 1.5em;
        color: #3498db;
        font-weight: bold;
    }
    
    .metadata {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-top: 30px;
        font-size: 0.9em;
        color: #6c757d;
    }

    
    @media (max-width: 1024px) {
        .parties-section {
            grid-template-columns: 1fr 1fr;
        }
    }

    @media (max-width: 768px) {
        .parties-section {
            grid-template-columns: 1fr;
        }

        .detail-row {
            flex-direction: column;
            align-items: flex-start;
        }

        .detail-label {
            margin-bottom: 5px;
        }

        .detail-value {
            text-align: right;
            direction: rtl;
        }
    }

</style>
{% endblock %}

{% block content %}
<div class="contract-header">
    <div class="contract-title">عقد بيع وشراء السيارات</div>
    <div class="contract-subtitle"></div>
</div>

<div class="parties-section">
    <div class="party-card">
        <div class="party-title">المالك الشرعي</div>
        <div class="party-name">{{ contract.legal_owner_name if contract.legal_owner_name else contract.name_1 if contract.name_1 else 'غير محدد' }}</div>
        {% if contract.legal_owner_address or contract.location_1 %}
        <div class="party-address">{{ contract.legal_owner_address if contract.legal_owner_address else contract.location_1 }}</div>
        {% endif %}
    </div>
    <div class="party-card">
        <div class="party-title">البائع</div>
        <div class="party-name">{{ contract.seller_name if contract.seller_name else contract.name_2 if contract.name_2 else 'غير محدد' }}</div>
        {% if contract.seller_address or contract.location_2 %}
        <div class="party-address">{{ contract.seller_address if contract.seller_address else contract.location_2 }}</div>
        {% endif %}
        {% if contract.seller_id or contract.id_1 %}
        <div class="party-id">رقم الهوية: {{ contract.seller_id if contract.seller_id else contract.id_1 }}</div>
        {% endif %}
        {% if contract.seller_phone or contract.phone_1 %}
        <div class="party-phone">الهاتف: {{ contract.seller_phone if contract.seller_phone else contract.phone_1 }}</div>
        {% endif %}
    </div>
    <div class="party-card">
        <div class="party-title">المشتري</div>
        <div class="party-name">{{ contract.buyer_name if contract.buyer_name else contract.name_3 if contract.name_3 else 'غير محدد' }}</div>
        {% if contract.buyer_address or contract.location_3 %}
        <div class="party-address">{{ contract.buyer_address if contract.buyer_address else contract.location_3 }}</div>
        {% endif %}
        {% if contract.buyer_id or contract.id_2 %}
        <div class="party-id">رقم الهوية: {{ contract.buyer_id if contract.buyer_id else contract.id_2 }}</div>
        {% endif %}
        {% if contract.buyer_phone or contract.phone_2 %}
        <div class="party-phone">الهاتف: {{ contract.buyer_phone if contract.buyer_phone else contract.phone_2 }}</div>
        {% endif %}
    </div>
</div>

<div class="amount-section">
    <div class="amount-title">سعر السيارة</div>
    <div class="amount-value">
        {% set sale_value = contract.sale_amount if contract.sale_amount is not none and contract.sale_amount != '' else (contract.badal_num if contract.badal_num is not none and contract.badal_num != '' else '0') %}
        {% set currency = contract.currency if contract.currency else contract.currency_type %}
        {% if currency == 'USD' %}
            ${{ "{:,.0f}".format(sale_value|float) }}
        {% else %}
            {{ "{:,.0f}".format(sale_value|float) }} دينار
        {% endif %}
    </div>
    {% if contract.amount_in_words %}
    <div class="amount-text">{{ contract.amount_in_words }}</div>
    {% endif %}

    <!-- المبلغ الواصل -->
    {% if (contract.paid_amount is not none and contract.paid_amount != '') or (contract.mony_num is not none and contract.mony_num != '') %}
    <div class="amount-title" style="margin-top: 20px;">المبلغ الواصل</div>
    <div class="amount-value">
        {% set paid_value = contract.paid_amount if contract.paid_amount is not none and contract.paid_amount != '' else (contract.mony_num if contract.mony_num is not none and contract.mony_num != '' else '0') %}
        {% if contract.currency == 'USD' or contract.currency_type == 'USD' %}
            ${{ "{:,.0f}".format(paid_value|float) }}
        {% else %}
            {{ "{:,.0f}".format(paid_value|float) }} دينار
        {% endif %}
    </div>
    {% endif %}

    <!-- المبلغ الباقي -->
    {% if (contract.remaining_amount is not none and contract.remaining_amount != '') or (contract.mony_not_delevired is not none and contract.mony_not_delevired != '') %}
    <div class="amount-title" style="margin-top: 20px;">المبلغ الباقي</div>
    <div class="amount-value">
        {% set remaining_value = contract.remaining_amount if contract.remaining_amount is not none and contract.remaining_amount != '' else (contract.mony_not_delevired if contract.mony_not_delevired is not none and contract.mony_not_delevired != '' else '0') %}
        {% if contract.currency == 'USD' or contract.currency_type == 'USD' %}
            ${{ "{:,.0f}".format(remaining_value|float) }}
        {% else %}
            {{ "{:,.0f}".format(remaining_value|float) }} دينار
        {% endif %}
    </div>
    {% endif %}

    <!-- ملاحظات الباقي -->
    {% if contract.m1 and contract.m1.strip() != '' %}
    <div class="amount-title" style="margin-top: 20px;">ملاحظات الباقي</div>
    <div class="amount-text">{{ contract.m1 }}</div>
    {% endif %}
</div>

<div class="contract-details">
    <div class="detail-row">
        <span class="detail-label">تاريخ العقد:</span>
        <span class="detail-value arabic">{{ contract.date }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">وقت العقد:</span>
        <span class="detail-value arabic">{{ contract.time if contract.time else contract.t if contract.t else 'غير محدد' }}</span>
    </div>

    {% if contract.period or contract.t_1 %}
    <div class="detail-row">
        <span class="detail-label">الفترة:</span>
        <span class="detail-value arabic">{{ contract.period if contract.period else contract.t_1 }}</span>
    </div>
    {% endif %}

    {% if contract.day %}
    <div class="detail-row">
        <span class="detail-label">اليوم:</span>
        <span class="detail-value arabic">{{ contract.day }}</span>
    </div>
    {% endif %}

    {% if contract.car_make or contract.car_type %}
    <div class="detail-row">
        <span class="detail-label">نوع السيارة:</span>
        <span class="detail-value arabic">{{ contract.car_make if contract.car_make else contract.car_type }}</span>
    </div>
    {% endif %}

    {% if contract.car_model %}
    <div class="detail-row">
        <span class="detail-label">موديل السيارة:</span>
        <span class="detail-value arabic">{{ contract.car_model }}</span>
    </div>
    {% endif %}

    {% if contract.car_year %}
    <div class="detail-row">
        <span class="detail-label">سنة الصنع:</span>
        <span class="detail-value">{{ contract.car_year }}</span>
    </div>
    {% endif %}

    {% if contract.car_color or contract.car_colar %}
    <div class="detail-row">
        <span class="detail-label">لون السيارة:</span>
        <span class="detail-value arabic">{{ contract.car_color if contract.car_color else contract.car_colar }}</span>
    </div>
    {% endif %}

    {% if contract.car_number or contract.car_num %}
    <div class="detail-row">
        <span class="detail-label">رقم السيارة:</span>
        <span class="detail-value">{{ contract.car_number if contract.car_number else contract.car_num }}</span>
    </div>
    {% endif %}

    {% if contract.car_city %}
    <div class="detail-row">
        <span class="detail-label">المدينة:</span>
        <span class="detail-value arabic">{{ contract.car_city }}</span>
    </div>
    {% endif %}

    {% if contract.car_property or contract.car_prop %}
    <div class="detail-row">
        <span class="detail-label">نوع السيارة:</span>
        <span class="detail-value arabic">{{ contract.car_property if contract.car_property else contract.car_prop }}</span>
    </div>
    {% endif %}

    {% if contract.annual_number or contract.sin_num %}
    <div class="detail-row">
        <span class="detail-label">رقم السنوية:</span>
        <span class="detail-value">{{ contract.annual_number if contract.annual_number else contract.sin_num }}</span>
    </div>
    {% endif %}

    {% if contract.chassis_number or contract.sasi_num %}
    <div class="detail-row">
        <span class="detail-label">رقم الشاصي:</span>
        <span class="detail-value">{{ contract.chassis_number if contract.chassis_number else contract.sasi_num }}</span>
    </div>
    {% endif %}

    {% if contract.note_a %}
    <div class="detail-row">
        <span class="detail-label">ملاحظة أ:</span>
        <span class="detail-value arabic">{{ contract.note_a }}</span>
    </div>
    {% endif %}

    {% if contract.note_b %}
    <div class="detail-row">
        <span class="detail-label">ملاحظة ب:</span>
        <span class="detail-value arabic">{{ contract.note_b }}</span>
    </div>
    {% endif %}
</div>

<!-- سجل التعديلات -->
{% if contract.modifications_history and contract.modifications_history|length > 0 %}
<div class="modifications-history" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; padding: 20px; margin: 30px 0;">
    <h3 style="color: #856404; margin-bottom: 20px; text-align: center; font-size: 1.5em;">📋 سجل التعديلات</h3>
    <div style="max-height: 400px; overflow-y: auto;">
        {% for modification in contract.modifications_history %}
        <div style="background: white; padding: 15px; margin-bottom: 10px; border-radius: 8px; border-left: 4px solid #ffc107; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="font-size: 1.1em; color: #333; margin-bottom: 8px;">
                <strong style="color: #856404;">{{ modification.field }}:</strong>
                تم التغيير من "<span style="color: #dc3545; font-weight: bold;">{{ modification.old_value }}</span>"
                إلى "<span style="color: #28a745; font-weight: bold;">{{ modification.new_value }}</span>"
            </div>
            <div style="font-size: 0.9em; color: #666; display: flex; justify-content: space-between; align-items: center;">
                <span>📅 {{ modification.timestamp }}</span>
                <span style="background: #007bff; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8em;">👤 {{ modification.modified_by }}</span>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% else %}
<div style="background: #ffebee; border: 1px solid #f44336; border-radius: 10px; padding: 20px; margin: 30px 0; text-align: center;">
    <h3 style="color: #d32f2f;">لا توجد تعديلات مسجلة</h3>
    <p style="color: #666;">لم يتم تسجيل أي تعديلات على هذا العقد بعد.</p>
</div>
{% endif %}

<div class="metadata">
    <strong>معلومات إضافية:</strong><br>
    UUID: {{ contract.uuid }}<br>
    تاريخ الإنشاء: {{ contract.created_at if contract.created_at else 'غير محدد' }}<br>
    الكمبيوتر: {{ contract.computer_name if contract.computer_name else 'غير محدد' }}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // منع النسخ والحفظ
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
    
    document.addEventListener('keydown', function(e) {
        // منع Ctrl+S, Ctrl+A, Ctrl+C, F12
        if ((e.ctrlKey && (e.key === 's' || e.key === 'a' || e.key === 'c')) || e.key === 'F12') {
            e.preventDefault();
        }
    });
    
    // منع السحب والإفلات
    document.addEventListener('dragstart', function(e) {
        e.preventDefault();
    });
</script>
{% endblock %}
