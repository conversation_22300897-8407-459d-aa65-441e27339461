#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثال للتكامل مع الموقع الحالي
================================

هذا مثال يوضح كيفية دمج مولد QR Code مع الموقع الحالي
"""

from qr_generator import generate_contract_qr, DOMAIN
import firebase_admin
from firebase_admin import credentials, db

def save_contract_with_qr(contract_data):
    """
    حفظ العقد في Firebase مع توليد QR Code
    
    Args:
        contract_data (dict): بيانات العقد
        
    Returns:
        tuple: (contract_uuid, qr_path, firebase_path)
    """
    
    # 1. توليد UUID للعقد (أو استخدام الموجود)
    if 'uuid' not in contract_data:
        import uuid
        contract_uuid = str(uuid.uuid4())
        contract_data['uuid'] = contract_uuid
    else:
        contract_uuid = contract_data['uuid']
    
    # 2. حفظ العقد في Firebase
    year = contract_data.get('date', '2024')[:4]  # استخراج السنة من التاريخ
    firebase_path = f"finalized_contracts/{year}/{contract_uuid}"
    
    try:
        ref = db.reference(firebase_path)
        ref.set(contract_data)
        print(f"تم حفظ العقد في Firebase: {firebase_path}")
    except Exception as e:
        print(f"خطأ في حفظ العقد: {e}")
        return None, None, None
    
    # 3. توليد QR Code
    qr_path = generate_contract_qr(contract_uuid)
    
    print(f"تم إنشاء العقد بنجاح:")
    print(f"UUID: {contract_uuid}")
    print(f"QR Code: {qr_path}")
    print(f"رابط العرض: {DOMAIN}/contract/{contract_uuid}")
    
    return contract_uuid, qr_path, firebase_path

def example_usage():
    """مثال للاستخدام"""
    
    # بيانات العقد التجريبية
    contract_data = {
        "serial_number": "2024-001",
        "seller_name": "أحمد محمد علي",
        "buyer_name": "علي حسن محمود",
        "sale_amount": "15000000",
        "paid_amount": "10000000", 
        "remaining_amount": "5000000",
        "currency": "IQD",
        "amount_in_words": "خمسة عشر مليون دينار عراقي",
        "date": "2024-01-15",
        "time": "14:30",
        "car_make": "تويوتا",
        "car_model": "كامري",
        "car_year": "2020",
        "chassis_number": "JTDKN3DU8L0123456",
        "computer_name": "OFFICE-PC-01",
        "created_at": "2024-01-15T14:30:00"
    }
    
    # حفظ العقد وتوليد QR
    uuid, qr_path, firebase_path = save_contract_with_qr(contract_data)
    
    if uuid:
        print("\n" + "="*50)
        print("تم إنشاء العقد بنجاح!")
        print(f"UUID: {uuid}")
        print(f"QR Code محفوظ في: {qr_path}")
        print(f"Firebase Path: {firebase_path}")
        print(f"رابط العرض: {DOMAIN}/contract/{uuid}")
        print("="*50)
    else:
        print("فشل في إنشاء العقد!")

if __name__ == "__main__":
    # تهيئة Firebase (يجب أن تكون معدة مسبقاً في الموقع الحالي)
    # firebase_admin.initialize_app(...)
    
    example_usage()
