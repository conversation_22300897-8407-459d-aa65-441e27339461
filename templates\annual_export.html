<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحميل عقود السنة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .main-content {
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .content {
            padding: 40px;
        }

        .year-selector {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .year-selector h2 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .year-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .year-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .year-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .year-btn.active {
            background: #e74c3c;
        }

        .contracts-info {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            display: none;
        }

        .contracts-info.show {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .info-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }

        .info-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }

        .contracts-table {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .contracts-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .contracts-table th,
        .contracts-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }

        .contracts-table th {
            background: #34495e;
            color: white;
            position: sticky;
            top: 0;
        }

        .contracts-table tr:hover {
            background: #f8f9fa;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #27ae60;
            color: white;
        }

        .btn-primary:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
            display: none;
        }

        .btn-danger:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .btn-danger.show {
            display: inline-block;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        /* أنماط شريط التقدم */
        #progressContainer {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }

        .progress-info h3 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-size: 20px;
        }

        .progress-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-label {
            display: block;
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .stat-item span:not(.stat-label) {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .progress-bar-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background-color: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
            border-radius: 12px;
            transition: width 0.5s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background-image: linear-gradient(
                -45deg,
                rgba(255, 255, 255, .2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, .2) 50%,
                rgba(255, 255, 255, .2) 75%,
                transparent 75%,
                transparent
            );
            background-size: 50px 50px;
            animation: move 2s linear infinite;
        }

        @keyframes move {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 50px 50px;
            }
        }

        .progress-status {
            text-align: center;
            font-weight: bold;
            color: #666;
            margin-top: 10px;
            font-size: 16px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        .success-message.show {
            display: block;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .password-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .password-modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .modal-content h3 {
            color: #e74c3c;
            margin-bottom: 20px;
        }

        .modal-content input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 20px;
            box-sizing: border-box;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .back-link {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    {% include 'toolbar.html' %}

    <div class="main-content">
        <div class="container">
        <div class="header">
            <h1>📊 تحميل عقود السنة</h1>
            <p>إدارة وتصدير العقود المبرمة حسب السنة</p>
        </div>

        <div class="content">
            <div class="year-selector">
                <h2>اختر السنة المراد تحميل عقودها</h2>
                <div class="year-buttons" id="yearButtons">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                <div class="loading" id="yearsLoading">
                    <div class="spinner"></div>
                    <p>جاري تحميل السنوات المتاحة...</p>
                </div>
            </div>

            <div class="contracts-info" id="contractsInfo">
                <div class="info-grid">
                    <div class="info-card">
                        <h3>السنة المختارة</h3>
                        <div class="number" id="selectedYear">-</div>
                    </div>
                    <div class="info-card">
                        <h3>عدد العقود</h3>
                        <div class="number" id="contractsCount">0</div>
                    </div>
                    <div class="info-card">
                        <h3>أول عقد</h3>
                        <div class="number" id="firstContract">-</div>
                    </div>
                    <div class="info-card">
                        <h3>آخر عقد</h3>
                        <div class="number" id="lastContract">-</div>
                    </div>
                </div>

                <div class="contracts-table" id="contractsTable">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" id="exportBtn" onclick="exportContracts()">
                        📄 تحميل العقود الأصلية (PDF)
                    </button>
                    <button class="btn btn-success" id="downloadBtn" onclick="downloadExportedFile()" style="display: none;">
                        💾 تحميل الملف الجاهز
                    </button>
                    <button class="btn btn-danger" id="deleteBtn" onclick="showDeleteModal()">
                        🗑️ حذف العقود المحملة
                    </button>
                </div>

                <!-- شريط التقدم -->
                <div id="progressContainer" style="display: none; margin-top: 20px;">
                    <div class="progress-info">
                        <h3 id="progressTitle">جاري تصدير العقود...</h3>
                        <div class="progress-stats">
                            <div class="stat-item">
                                <span class="stat-label">التقدم:</span>
                                <span id="progressPercent">0%</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">العقود المُعالجة:</span>
                                <span id="processedContracts">0</span> / <span id="totalContracts">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">المجموعات:</span>
                                <span id="currentBatch">0</span> / <span id="totalBatches">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">الوقت المتبقي:</span>
                                <span id="estimatedTime">حساب...</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">معدل النجاح:</span>
                                <span id="successRate">100%</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">السرعة:</span>
                                <span id="processingSpeed">حساب...</span>
                            </div>
                        </div>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progressBar">
                            <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
                        </div>
                    </div>
                    <div class="progress-status" id="progressStatus">بدء العملية...</div>
                </div>

                <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #3498db;">
                    <h4 style="color: #2980b9; margin: 0 0 10px 0;">📋 معلومات التصدير</h4>
                    <p style="margin: 5px 0; color: #34495e;">• سيتم إنشاء ملف PDF واحد يحتوي على جميع العقود</p>
                    <p style="margin: 5px 0; color: #34495e;">• كل صفحة ستحتوي على عقد كامل بتصميمه الأصلي</p>
                    <p style="margin: 5px 0; color: #34495e;">• سيتم ملء جميع البيانات والصور الأصلية</p>
                    <p style="margin: 5px 0; color: #34495e;">• العقود مرتبة حسب الرقم التسلسلي</p>
                </div>
            </div>

            <div class="loading" id="contractsLoading">
                <div class="spinner"></div>
                <p>جاري تحميل بيانات العقود...</p>
            </div>

            <div class="success-message" id="successMessage"></div>
            <div class="error-message" id="errorMessage"></div>
        </div>
    </div>

    <!-- نافذة تأكيد كلمة المرور -->
    <div class="password-modal" id="passwordModal">
        <div class="modal-content">
            <h3>⚠️ تأكيد حذف العقود</h3>
            <p>هذا الإجراء سيحذف جميع عقود السنة المختارة نهائياً من قاعدة البيانات!</p>
            <p><strong>يرجى إدخال كلمة مرور الإدارة للتأكيد:</strong></p>
            <input type="password" id="adminPassword" placeholder="كلمة مرور الإدارة">
            <div class="modal-buttons">
                <button class="btn btn-danger" onclick="confirmDelete()">تأكيد الحذف</button>
                <button class="btn btn-secondary" onclick="hideDeleteModal()">إلغاء</button>
            </div>
        </div>
    </div>

    <script>
        let selectedYear = null;
        let contractsData = [];

        // تحميل السنوات المتاحة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAvailableYears();
        });

        // تحميل السنوات المتاحة
        async function loadAvailableYears() {
            const loading = document.getElementById('yearsLoading');
            const buttonsContainer = document.getElementById('yearButtons');
            
            loading.classList.add('show');
            
            try {
                const response = await fetch('/admin/api/available_years');
                const data = await response.json();
                
                if (data.success) {
                    buttonsContainer.innerHTML = '';
                    
                    if (data.years.length === 0) {
                        buttonsContainer.innerHTML = '<p>لا توجد عقود مبرمة حتى الآن</p>';
                    } else {
                        data.years.forEach(year => {
                            const button = document.createElement('button');
                            button.className = 'year-btn';
                            button.textContent = year;
                            button.onclick = () => selectYear(year);
                            buttonsContainer.appendChild(button);
                        });
                    }
                } else {
                    showError('فشل في تحميل السنوات المتاحة');
                }
            } catch (error) {
                showError('خطأ في الاتصال بالخادم');
            } finally {
                loading.classList.remove('show');
            }
        }

        // اختيار سنة
        async function selectYear(year) {
            selectedYear = year;
            
            // تحديث أزرار السنوات
            document.querySelectorAll('.year-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent == year) {
                    btn.classList.add('active');
                }
            });
            
            // إخفاء معلومات العقود وإظهار التحميل
            document.getElementById('contractsInfo').classList.remove('show');
            document.getElementById('contractsLoading').classList.add('show');
            
            try {
                const response = await fetch(`/admin/api/contracts_by_year/${year}`);
                const data = await response.json();
                
                if (data.success) {
                    contractsData = data.contracts;
                    displayContractsInfo(data);
                } else {
                    showError(data.error || 'فشل في تحميل عقود السنة');
                }
            } catch (error) {
                showError('خطأ في الاتصال بالخادم');
            } finally {
                document.getElementById('contractsLoading').classList.remove('show');
            }
        }

        // عرض معلومات العقود
        function displayContractsInfo(data) {
            document.getElementById('selectedYear').textContent = data.year;
            document.getElementById('contractsCount').textContent = data.total_count;
            
            if (data.contracts.length > 0) {
                const sortedContracts = data.contracts.sort((a, b) => 
                    parseInt(a.serial_number || 0) - parseInt(b.serial_number || 0)
                );
                
                document.getElementById('firstContract').textContent = sortedContracts[0].serial_number || '-';
                document.getElementById('lastContract').textContent = sortedContracts[sortedContracts.length - 1].serial_number || '-';
                
                // إنشاء جدول العقود
                const tableHTML = `
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم التسلسلي</th>
                                <th>اسم البائع</th>
                                <th>اسم المشتري</th>
                                <th>نوع السيارة</th>
                                <th>مبلغ البيع</th>
                                <th>تاريخ الإبرام</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sortedContracts.map(contract => `
                                <tr>
                                    <td>${contract.serial_number || '-'}</td>
                                    <td>${contract.seller_name || '-'}</td>
                                    <td>${contract.buyer_name || '-'}</td>
                                    <td>${contract.car_type || '-'}</td>
                                    <td>${contract.sale_amount || '-'}</td>
                                    <td>${contract.finalized_at ? contract.finalized_at.split(' ')[0] : '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
                
                document.getElementById('contractsTable').innerHTML = tableHTML;
            } else {
                document.getElementById('firstContract').textContent = '-';
                document.getElementById('lastContract').textContent = '-';
                document.getElementById('contractsTable').innerHTML = '<p style="text-align: center; padding: 20px;">لا توجد عقود لهذه السنة</p>';
            }
            
            document.getElementById('contractsInfo').classList.add('show');
        }

        // تصدير العقود مع تتبع التقدم
        async function exportContracts() {
            if (!selectedYear) {
                showError('يرجى اختيار سنة أولاً');
                return;
            }

            const exportBtn = document.getElementById('exportBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            const progressContainer = document.getElementById('progressContainer');

            // إخفاء الأزرار وإظهار شريط التقدم
            exportBtn.style.display = 'none';
            downloadBtn.style.display = 'none';
            progressContainer.style.display = 'block';

            // إعادة تعيين شريط التقدم
            resetProgressBar();

            try {
                // بدء عملية التصدير
                const response = await fetch(`/admin/api/export_year_contracts_async/${selectedYear}`);
                const data = await response.json();

                if (data.success) {
                    // بدء تتبع التقدم
                    trackExportProgress();
                } else {
                    showError(data.error || 'فشل في بدء عملية التصدير');
                    resetExportUI();
                }
            } catch (error) {
                showError('خطأ في بدء عملية التصدير');
                resetExportUI();
            }
        }

        // تتبع تقدم التصدير مع تحسينات الأداء
        async function trackExportProgress() {
            let consecutiveErrors = 0;
            const maxErrors = 3;

            const progressInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/admin/api/export_progress/${selectedYear}`);
                    const data = await response.json();

                    if (data.success) {
                        consecutiveErrors = 0; // إعادة تعيين عداد الأخطاء
                        const progress = data.progress;
                        updateProgressBar(progress);

                        if (progress.status === 'completed') {
                            clearInterval(progressInterval);
                            showExportCompleted();
                        } else if (progress.status === 'error') {
                            clearInterval(progressInterval);
                            showError(progress.error || 'حدث خطأ أثناء التصدير');
                            resetExportUI();
                        }
                    } else {
                        consecutiveErrors++;
                        if (consecutiveErrors >= maxErrors) {
                            clearInterval(progressInterval);
                            showError('فشل في تتبع تقدم التصدير بعد عدة محاولات');
                            resetExportUI();
                        }
                    }
                } catch (error) {
                    console.error('خطأ في تتبع التقدم:', error);
                    consecutiveErrors++;
                    if (consecutiveErrors >= maxErrors) {
                        clearInterval(progressInterval);
                        showError('فشل في الاتصال بالخادم لتتبع التقدم');
                        resetExportUI();
                    }
                }
            }, 5000); // تحديث كل 5 ثوانٍ بدلاً من ثانيتين لتوفير الموارد
        }

        // تحديث شريط التقدم مع معلومات محسنة
        function updateProgressBar(progress) {
            document.getElementById('progressPercent').textContent = Math.round(progress.progress) + '%';
            document.getElementById('processedContracts').textContent = progress.processed_contracts || 0;
            document.getElementById('totalContracts').textContent = progress.total_contracts || 0;
            document.getElementById('currentBatch').textContent = progress.current_batch || 0;
            document.getElementById('totalBatches').textContent = progress.total_batches || 0;

            // تحديث الوقت المتبقي مع تحسينات
            const estimatedTime = progress.estimated_time_remaining || 0;
            if (estimatedTime > 0) {
                const minutes = Math.floor(estimatedTime / 60);
                const seconds = Math.floor(estimatedTime % 60);
                document.getElementById('estimatedTime').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            } else {
                document.getElementById('estimatedTime').textContent = 'حساب...';
            }

            // تحديث معدل النجاح
            const successRate = progress.success_rate ? Math.round(progress.success_rate) : 100;
            document.getElementById('successRate').textContent = successRate + '%';

            // تحديث السرعة
            const speed = progress.processing_speed ? Math.round(progress.processing_speed * 60) : 0;
            if (speed > 0) {
                document.getElementById('processingSpeed').textContent = speed + ' عقد/دقيقة';
            } else {
                document.getElementById('processingSpeed').textContent = 'حساب...';
            }

            // تحديث شريط التقدم المرئي مع تأثير بصري
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = progress.progress + '%';

            // تغيير لون شريط التقدم حسب المرحلة
            if (progress.status === 'merging') {
                progressFill.style.background = 'linear-gradient(90deg, #28a745, #20c997)';
            } else if (progress.progress > 80) {
                progressFill.style.background = 'linear-gradient(90deg, #007bff, #0056b3)';
            } else {
                progressFill.style.background = 'linear-gradient(90deg, #007bff, #0056b3)';
            }

            // تحديث حالة التقدم مع معلومات إضافية
            let statusText = 'جاري المعالجة...';
            if (progress.status === 'initializing') {
                statusText = 'تحضير البيانات وتهيئة النظام...';
            } else if (progress.status === 'processing') {
                const successRate = progress.success_rate ? Math.round(progress.success_rate) : 100;
                const speed = progress.processing_speed ? Math.round(progress.processing_speed * 60) : 0;
                statusText = `معالجة المجموعة ${progress.current_batch} من ${progress.total_batches} - معدل النجاح: ${successRate}%`;
                if (speed > 0) {
                    statusText += ` - السرعة: ${speed} عقد/دقيقة`;
                }
            } else if (progress.status === 'merging') {
                statusText = 'دمج ملفات PDF في ملف واحد...';
            }

            document.getElementById('progressStatus').textContent = statusText;
        }

        // إعادة تعيين شريط التقدم
        function resetProgressBar() {
            document.getElementById('progressPercent').textContent = '0%';
            document.getElementById('processedContracts').textContent = '0';
            document.getElementById('totalContracts').textContent = '0';
            document.getElementById('currentBatch').textContent = '0';
            document.getElementById('totalBatches').textContent = '0';
            document.getElementById('estimatedTime').textContent = 'حساب...';
            document.getElementById('successRate').textContent = '100%';
            document.getElementById('processingSpeed').textContent = 'حساب...';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressFill').style.background = 'linear-gradient(90deg, #007bff, #0056b3)';
            document.getElementById('progressStatus').textContent = 'بدء العملية...';
        }

        // إظهار اكتمال التصدير
        function showExportCompleted() {
            document.getElementById('progressTitle').textContent = '✅ تم إنشاء الملف بنجاح!';
            document.getElementById('progressStatus').textContent = 'الملف جاهز للتحميل';
            document.getElementById('progressFill').style.width = '100%';

            // إظهار زر التحميل
            document.getElementById('downloadBtn').style.display = 'inline-block';
            document.getElementById('deleteBtn').classList.add('show');

            showSuccess(`تم إنشاء ملف عقود سنة ${selectedYear} بنجاح! يمكنك الآن تحميله.`);
        }

        // إعادة تعيين واجهة التصدير
        function resetExportUI() {
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('exportBtn').style.display = 'inline-block';
            document.getElementById('downloadBtn').style.display = 'none';
        }

        // تحميل الملف المُصدر
        async function downloadExportedFile() {
            if (!selectedYear) {
                showError('يرجى اختيار سنة أولاً');
                return;
            }

            const downloadBtn = document.getElementById('downloadBtn');
            const originalText = downloadBtn.textContent;

            downloadBtn.textContent = '⏳ جاري التحميل...';
            downloadBtn.disabled = true;

            try {
                const response = await fetch(`/admin/api/download_exported_contracts/${selectedYear}`);

                if (response.ok) {
                    // تحميل الملف
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `contracts_export_${selectedYear}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    // إخفاء زر التحميل بعد التحميل الناجح
                    downloadBtn.style.display = 'none';
                    document.getElementById('exportBtn').style.display = 'inline-block';
                    document.getElementById('progressContainer').style.display = 'none';

                    showSuccess(`تم تحميل عقود سنة ${selectedYear} بنجاح!`);
                } else {
                    const errorData = await response.json();
                    showError(errorData.error || 'فشل في تحميل الملف');
                }
            } catch (error) {
                showError('خطأ في تحميل الملف');
            } finally {
                downloadBtn.textContent = originalText;
                downloadBtn.disabled = false;
            }
        }

        // إظهار نافذة تأكيد الحذف
        function showDeleteModal() {
            document.getElementById('passwordModal').classList.add('show');
            document.getElementById('adminPassword').value = '';
            document.getElementById('adminPassword').focus();
        }

        // إخفاء نافذة تأكيد الحذف
        function hideDeleteModal() {
            document.getElementById('passwordModal').classList.remove('show');
        }

        // تأكيد الحذف
        async function confirmDelete() {
            const password = document.getElementById('adminPassword').value;

            if (!password) {
                showError('يرجى إدخال كلمة مرور الإدارة');
                return;
            }

            const confirmBtn = document.querySelector('.modal-buttons .btn-danger');
            const originalText = confirmBtn.textContent;

            confirmBtn.textContent = '⏳ جاري الحذف...';
            confirmBtn.disabled = true;

            try {
                const response = await fetch(`/admin/api/delete_year_contracts/${selectedYear}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        admin_password: password
                    })
                });

                const data = await response.json();

                if (data.success) {
                    confirmBtn.textContent = '✅ تم الحذف';
                    showSuccess(data.message);

                    setTimeout(() => {
                        hideDeleteModal();
                        // إخفاء زر الحذف وإعادة تحميل البيانات
                        document.getElementById('deleteBtn').classList.remove('show');
                        selectYear(selectedYear); // إعادة تحميل بيانات السنة
                        loadAvailableYears(); // إعادة تحميل السنوات المتاحة
                    }, 2000);
                } else {
                    showError(data.error || 'فشل في حذف العقود');
                    confirmBtn.textContent = originalText;
                    confirmBtn.disabled = false;
                }
            } catch (error) {
                showError('خطأ في حذف العقود');
                confirmBtn.textContent = originalText;
                confirmBtn.disabled = false;
            }
        }

        // إظهار رسالة نجاح
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.classList.add('show');
            
            setTimeout(() => {
                successDiv.classList.remove('show');
            }, 5000);
        }

        // إظهار رسالة خطأ
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.classList.add('show');
            
            setTimeout(() => {
                errorDiv.classList.remove('show');
            }, 5000);
        }

        // إغلاق النافذة المنبثقة بالضغط على Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                hideDeleteModal();
            }
        });

        // إغلاق النافذة المنبثقة بالضغط خارجها
        document.getElementById('passwordModal').addEventListener('click', function(event) {
            if (event.target === this) {
                hideDeleteModal();
            }
        });
    </script>
        </div>
    </div>
</body>
</html>
