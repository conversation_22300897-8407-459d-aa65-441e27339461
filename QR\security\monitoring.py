"""
Advanced Security Monitoring System
نظام المراقبة الأمنية المتقدم

This module provides comprehensive security monitoring including:
- Real-time threat detection
- Anomaly detection
- Security event logging
- Automated response to threats

يوفر هذا الملف مراقبة أمنية شاملة تشمل:
- اكتشاف التهديدات في الوقت الفعلي
- اكتشاف الشذوذ
- تسجيل الأحداث الأمنية
- الاستجابة التلقائية للتهديدات
"""

import os
import json
import time
import hashlib
import threading
from datetime import datetime, timedelta
from collections import defaultdict, deque
from flask import request
import logging

logger = logging.getLogger(__name__)

class SecurityMonitor:
    """
    Advanced security monitoring with real-time threat detection
    مراقبة أمنية متقدمة مع اكتشاف التهديدات في الوقت الفعلي
    """
    
    def __init__(self):
        self.threat_patterns = {
            'sql_injection': [
                r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
                r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
                r"(--|#|/\*|\*/)",
                r"(\bxp_cmdshell\b|\bsp_executesql\b)"
            ],
            'xss_attack': [
                r"(<script[^>]*>.*?</script>)",
                r"(javascript:|vbscript:|onload=|onerror=|onclick=)",
                r"(<iframe|<object|<embed|<applet)"
            ],
            'path_traversal': [
                r"(\.\./|\.\.\\\|%2e%2e%2f|%2e%2e%5c)"
            ],
            'command_injection': [
                r"(;|\||&|`|\$\(|\${)",
                r"(\b(cat|ls|dir|type|echo|ping|wget|curl|nc|netcat)\b)"
            ]
        }
        
        self.security_events = deque(maxlen=10000)  # Keep last 10k events
        self.ip_activity = defaultdict(list)
        self.blocked_ips = set()
        self.suspicious_ips = defaultdict(int)
        
        # Threat thresholds
        self.max_requests_per_minute = 60
        self.max_failed_attempts = 5
        self.suspicious_threshold = 10
        self.auto_block_threshold = 20
        
        # Start monitoring thread
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("🔒 Security monitoring system initialized")
    
    def log_security_event(self, event_type: str, details: dict, severity: str = 'medium'):
        """
        Log security event with threat analysis
        تسجيل حدث أمني مع تحليل التهديد
        """
        try:
            event = {
                'timestamp': datetime.utcnow().isoformat(),
                'event_type': event_type,
                'severity': severity,
                'ip': request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr) if request else 'unknown',
                'user_agent': request.headers.get('User-Agent', '') if request else '',
                'endpoint': request.endpoint if request else '',
                'method': request.method if request else '',
                'details': details
            }
            
            # Add to events queue
            self.security_events.append(event)
            
            # Log to file
            security_log_file = os.path.join(os.path.dirname(__file__), '..', 'security_events.log')
            with open(security_log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(event, ensure_ascii=False) + '\n')
            
            # Analyze threat level
            self._analyze_threat(event)
            
            # Log to console based on severity
            if severity == 'critical':
                logger.critical(f"🚨 CRITICAL SECURITY EVENT: {event_type} - {details}")
            elif severity == 'high':
                logger.error(f"🔴 HIGH SECURITY EVENT: {event_type} - {details}")
            elif severity == 'medium':
                logger.warning(f"🟡 SECURITY EVENT: {event_type} - {details}")
            else:
                logger.info(f"🔵 SECURITY INFO: {event_type} - {details}")
                
        except Exception as e:
            logger.error(f"❌ Failed to log security event: {e}")
    
    def detect_attack_patterns(self, data: str) -> list:
        """
        Detect attack patterns in input data
        اكتشاف أنماط الهجمات في البيانات المدخلة
        """
        detected_attacks = []
        
        try:
            for attack_type, patterns in self.threat_patterns.items():
                for pattern in patterns:
                    import re
                    if re.search(pattern, data, re.IGNORECASE):
                        detected_attacks.append(attack_type)
                        break
            
            return detected_attacks
            
        except Exception as e:
            logger.error(f"❌ Attack pattern detection failed: {e}")
            return []
    
    def analyze_request_anomaly(self, request_data: dict) -> dict:
        """
        Analyze request for anomalies
        تحليل الطلب للشذوذ
        """
        anomalies = []
        risk_score = 0
        
        try:
            # Check request size
            request_size = len(json.dumps(request_data))
            if request_size > 100000:  # 100KB
                anomalies.append('oversized_request')
                risk_score += 30
            
            # Check parameter count
            if len(request_data) > 50:
                anomalies.append('too_many_parameters')
                risk_score += 20
            
            # Check for suspicious patterns in values
            for key, value in request_data.items():
                if isinstance(value, str):
                    attacks = self.detect_attack_patterns(value)
                    if attacks:
                        anomalies.extend(attacks)
                        risk_score += 50
            
            # Check for unusual headers
            if request:
                suspicious_headers = ['x-forwarded-for', 'x-real-ip', 'x-originating-ip']
                for header in suspicious_headers:
                    if header in request.headers:
                        risk_score += 10
            
            return {
                'anomalies': anomalies,
                'risk_score': risk_score,
                'threat_level': self._calculate_threat_level(risk_score)
            }
            
        except Exception as e:
            logger.error(f"❌ Request anomaly analysis failed: {e}")
            return {'anomalies': [], 'risk_score': 0, 'threat_level': 'low'}
    
    def track_ip_activity(self, ip: str, event_type: str):
        """
        Track IP activity for behavioral analysis
        تتبع نشاط IP للتحليل السلوكي
        """
        try:
            current_time = time.time()
            
            # Clean old entries (older than 1 hour)
            self.ip_activity[ip] = [
                (timestamp, event) for timestamp, event in self.ip_activity[ip]
                if current_time - timestamp < 3600
            ]
            
            # Add current activity
            self.ip_activity[ip].append((current_time, event_type))
            
            # Analyze activity pattern
            recent_activity = [
                event for timestamp, event in self.ip_activity[ip]
                if current_time - timestamp < 300  # Last 5 minutes
            ]
            
            # Check for suspicious patterns
            if len(recent_activity) > self.max_requests_per_minute:
                self.suspicious_ips[ip] += 5
                self.log_security_event('high_frequency_requests', {
                    'ip': ip,
                    'request_count': len(recent_activity),
                    'time_window': '5_minutes'
                }, 'high')
            
            # Check for failed attempts
            failed_attempts = [event for event in recent_activity if 'failed' in event]
            if len(failed_attempts) > self.max_failed_attempts:
                self.suspicious_ips[ip] += 10
                self.log_security_event('multiple_failed_attempts', {
                    'ip': ip,
                    'failed_count': len(failed_attempts)
                }, 'high')
            
            # Auto-block if threshold exceeded
            if self.suspicious_ips[ip] >= self.auto_block_threshold:
                self.block_ip(ip, 'automated_threat_detection')
                
        except Exception as e:
            logger.error(f"❌ IP activity tracking failed: {e}")
    
    def block_ip(self, ip: str, reason: str):
        """
        Block suspicious IP address
        حظر عنوان IP المشبوه
        """
        try:
            self.blocked_ips.add(ip)
            
            self.log_security_event('ip_blocked', {
                'ip': ip,
                'reason': reason,
                'suspicious_score': self.suspicious_ips.get(ip, 0)
            }, 'critical')
            
            logger.critical(f"🚫 IP BLOCKED: {ip} - Reason: {reason}")
            
        except Exception as e:
            logger.error(f"❌ IP blocking failed: {e}")
    
    def is_ip_blocked(self, ip: str) -> bool:
        """Check if IP is blocked"""
        return ip in self.blocked_ips
    
    def _analyze_threat(self, event: dict):
        """
        Analyze threat level and take automated actions
        تحليل مستوى التهديد واتخاذ إجراءات تلقائية
        """
        try:
            ip = event.get('ip', 'unknown')
            event_type = event.get('event_type', '')
            
            # Track IP activity
            self.track_ip_activity(ip, event_type)
            
            # Critical events trigger immediate response
            critical_events = [
                'sql_injection_detected',
                'xss_attack_detected',
                'command_injection_detected',
                'multiple_failed_logins'
            ]
            
            if event_type in critical_events:
                self.suspicious_ips[ip] += 15
                
                if self.suspicious_ips[ip] >= self.auto_block_threshold:
                    self.block_ip(ip, f'critical_event_{event_type}')
                    
        except Exception as e:
            logger.error(f"❌ Threat analysis failed: {e}")
    
    def _calculate_threat_level(self, risk_score: int) -> str:
        """Calculate threat level based on risk score"""
        if risk_score >= 80:
            return 'critical'
        elif risk_score >= 50:
            return 'high'
        elif risk_score >= 20:
            return 'medium'
        else:
            return 'low'
    
    def _monitor_loop(self):
        """
        Background monitoring loop
        حلقة المراقبة في الخلفية
        """
        while self.monitoring_active:
            try:
                # Clean old data every 5 minutes
                current_time = time.time()
                
                # Clean IP activity older than 1 hour
                for ip in list(self.ip_activity.keys()):
                    self.ip_activity[ip] = [
                        (timestamp, event) for timestamp, event in self.ip_activity[ip]
                        if current_time - timestamp < 3600
                    ]
                    
                    if not self.ip_activity[ip]:
                        del self.ip_activity[ip]
                
                # Reduce suspicious scores over time
                for ip in list(self.suspicious_ips.keys()):
                    self.suspicious_ips[ip] = max(0, self.suspicious_ips[ip] - 1)
                    if self.suspicious_ips[ip] == 0:
                        del self.suspicious_ips[ip]
                
                time.sleep(300)  # Sleep for 5 minutes
                
            except Exception as e:
                logger.error(f"❌ Monitoring loop error: {e}")
                time.sleep(60)  # Sleep for 1 minute on error
    
    def get_security_stats(self) -> dict:
        """
        Get security monitoring statistics
        الحصول على إحصائيات المراقبة الأمنية
        """
        try:
            recent_events = [
                event for event in self.security_events
                if datetime.fromisoformat(event['timestamp']) > datetime.utcnow() - timedelta(hours=24)
            ]
            
            return {
                'total_events_24h': len(recent_events),
                'blocked_ips': len(self.blocked_ips),
                'suspicious_ips': len(self.suspicious_ips),
                'active_monitoring': self.monitoring_active,
                'recent_events': list(self.security_events)[-10:],  # Last 10 events
                'threat_summary': self._generate_threat_summary(recent_events)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get security stats: {e}")
            return {}
    
    def _generate_threat_summary(self, events: list) -> dict:
        """Generate threat summary from events"""
        summary = defaultdict(int)
        
        for event in events:
            event_type = event.get('event_type', 'unknown')
            severity = event.get('severity', 'low')
            summary[f"{severity}_{event_type}"] += 1
        
        return dict(summary)

# Global security monitor instance
security_monitor = SecurityMonitor()

# Convenience functions
def log_security_event(event_type: str, details: dict, severity: str = 'medium'):
    """Log security event"""
    security_monitor.log_security_event(event_type, details, severity)

def detect_attack_patterns(data: str) -> list:
    """Detect attack patterns"""
    return security_monitor.detect_attack_patterns(data)

def analyze_request_anomaly(request_data: dict) -> dict:
    """Analyze request anomaly"""
    return security_monitor.analyze_request_anomaly(request_data)

def is_ip_blocked(ip: str) -> bool:
    """Check if IP is blocked"""
    return security_monitor.is_ip_blocked(ip)

def get_security_stats() -> dict:
    """Get security statistics"""
    return security_monitor.get_security_stats()
