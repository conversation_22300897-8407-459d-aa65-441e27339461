"""
Advanced Encryption System for Sensitive Data
نظام تشفير متقدم للبيانات الحساسة

This module provides AES-256 encryption with secure key management
يوفر هذا الملف تشفير AES-256 مع إدارة آمنة للمفاتيح
"""

import os
import base64
import hashlib
import secrets
from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import logging

logger = logging.getLogger(__name__)

class SecureEncryption:
    """
    Advanced encryption class with multiple security layers
    فئة التشفير المتقدم مع طبقات أمان متعددة
    """
    
    def __init__(self):
        self.backend = default_backend()
        self._master_key = None
        self._salt = None
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize encryption with secure key derivation"""
        try:
            # Generate or load master key
            master_password = os.getenv('MASTER_ENCRYPTION_KEY')
            if not master_password:
                # Generate a secure master password if not exists
                master_password = secrets.token_urlsafe(64)
                logger.warning("⚠️ Generated new master encryption key. Store it securely!")
                logger.warning(f"MASTER_ENCRYPTION_KEY={master_password}")
            
            # Generate or load salt
            salt_file = os.path.join(os.path.dirname(__file__), '.salt')
            if os.path.exists(salt_file):
                with open(salt_file, 'rb') as f:
                    self._salt = f.read()
            else:
                self._salt = os.urandom(32)
                with open(salt_file, 'wb') as f:
                    f.write(self._salt)
                os.chmod(salt_file, 0o600)  # Restrict file permissions
            
            # Derive encryption key using PBKDF2
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=self._salt,
                iterations=100000,
                backend=self.backend
            )
            self._master_key = kdf.derive(master_password.encode())
            
            logger.info("🔐 Encryption system initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize encryption: {e}")
            raise
    
    def encrypt_data(self, data: str) -> str:
        """
        Encrypt sensitive data using AES-256
        تشفير البيانات الحساسة باستخدام AES-256
        """
        try:
            if not isinstance(data, str):
                data = str(data)
            
            # Generate random IV for each encryption
            iv = os.urandom(16)
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(self._master_key),
                modes.CBC(iv),
                backend=self.backend
            )
            encryptor = cipher.encryptor()
            
            # Pad data to block size
            padded_data = self._pad_data(data.encode())
            
            # Encrypt data
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
            
            # Combine IV and encrypted data
            result = iv + encrypted_data
            
            # Return base64 encoded result
            return base64.b64encode(result).decode()
            
        except Exception as e:
            logger.error(f"❌ Encryption failed: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """
        Decrypt sensitive data
        فك تشفير البيانات الحساسة
        """
        try:
            # Decode base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            
            # Extract IV and encrypted data
            iv = encrypted_bytes[:16]
            encrypted_content = encrypted_bytes[16:]
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(self._master_key),
                modes.CBC(iv),
                backend=self.backend
            )
            decryptor = cipher.decryptor()
            
            # Decrypt data
            padded_data = decryptor.update(encrypted_content) + decryptor.finalize()
            
            # Remove padding
            data = self._unpad_data(padded_data)
            
            return data.decode()
            
        except Exception as e:
            logger.error(f"❌ Decryption failed: {e}")
            raise
    
    def _pad_data(self, data: bytes) -> bytes:
        """Add PKCS7 padding to data"""
        block_size = 16
        padding_length = block_size - (len(data) % block_size)
        padding = bytes([padding_length] * padding_length)
        return data + padding
    
    def _unpad_data(self, padded_data: bytes) -> bytes:
        """Remove PKCS7 padding from data"""
        padding_length = padded_data[-1]
        return padded_data[:-padding_length]
    
    def hash_password(self, password: str) -> str:
        """
        Create secure password hash using bcrypt-like method
        إنشاء hash آمن لكلمة المرور
        """
        try:
            # Generate random salt for password
            salt = os.urandom(32)
            
            # Create hash using PBKDF2
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=64,
                salt=salt,
                iterations=100000,
                backend=self.backend
            )
            password_hash = kdf.derive(password.encode())
            
            # Combine salt and hash
            result = salt + password_hash
            
            return base64.b64encode(result).decode()
            
        except Exception as e:
            logger.error(f"❌ Password hashing failed: {e}")
            raise
    
    def verify_password(self, password: str, stored_hash: str) -> bool:
        """
        Verify password against stored hash
        التحقق من كلمة المرور مقابل الـ hash المحفوظ
        """
        try:
            # Decode stored hash
            hash_bytes = base64.b64decode(stored_hash.encode())
            
            # Extract salt and hash
            salt = hash_bytes[:32]
            stored_password_hash = hash_bytes[32:]
            
            # Create hash with same salt
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=64,
                salt=salt,
                iterations=100000,
                backend=self.backend
            )
            password_hash = kdf.derive(password.encode())
            
            # Compare hashes securely
            return secrets.compare_digest(password_hash, stored_password_hash)
            
        except Exception as e:
            logger.error(f"❌ Password verification failed: {e}")
            return False

# Global encryption instance
encryption_manager = SecureEncryption()

# Convenience functions
def encrypt_sensitive_data(data: str) -> str:
    """Encrypt sensitive data"""
    return encryption_manager.encrypt_data(data)

def decrypt_sensitive_data(encrypted_data: str) -> str:
    """Decrypt sensitive data"""
    return encryption_manager.decrypt_data(encrypted_data)

def hash_password(password: str) -> str:
    """Hash password securely"""
    return encryption_manager.hash_password(password)

def verify_password(password: str, stored_hash: str) -> bool:
    """Verify password"""
    return encryption_manager.verify_password(password, stored_hash)
