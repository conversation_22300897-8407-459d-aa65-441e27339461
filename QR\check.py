#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Contract Viewer - Configuration Checker
==========================================

فحص الإعدادات والتأكد من صحة التكوين
"""

import os
import sys
import json
from dotenv import load_dotenv

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    version = sys.version_info
    
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - مدعوم")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - غير مدعوم")
        print("يرجى استخدام Python 3.8 أو أحدث")
        return False

def check_env_file():
    """فحص ملف .env"""
    print("\n📄 فحص ملف .env...")
    
    if not os.path.exists('.env'):
        print("❌ ملف .env غير موجود!")
        print("يرجى نسخ .env.example إلى .env وتعديل القيم")
        return False
    
    print("✅ ملف .env موجود")
    
    # تحميل متغيرات البيئة
    load_dotenv()
    
    required_vars = [
        'FIREBASE_PROJECT_ID',
        'FIREBASE_PRIVATE_KEY',
        'FIREBASE_CLIENT_EMAIL',
        'FIREBASE_DATABASE_URL',
        'SECRET_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value.startswith('your-'):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ متغيرات البيئة التالية مفقودة أو لم يتم تعديلها:")
        for var in missing_vars:
            print(f"   - {var}")
        return False
    
    print("✅ جميع متغيرات البيئة المطلوبة موجودة")
    return True

def check_firebase_config():
    """فحص إعدادات Firebase"""
    print("\n🔥 فحص إعدادات Firebase...")
    
    try:
        project_id = os.getenv('FIREBASE_PROJECT_ID')
        private_key = os.getenv('FIREBASE_PRIVATE_KEY')
        client_email = os.getenv('FIREBASE_CLIENT_EMAIL')
        database_url = os.getenv('FIREBASE_DATABASE_URL')
        
        # فحص Project ID
        if not project_id or project_id == 'your-project-id':
            print("❌ FIREBASE_PROJECT_ID غير صحيح")
            return False
        
        # فحص Private Key
        if not private_key or 'BEGIN PRIVATE KEY' not in private_key:
            print("❌ FIREBASE_PRIVATE_KEY غير صحيح")
            return False
        
        # فحص Client Email
        if not client_email or '@' not in client_email:
            print("❌ FIREBASE_CLIENT_EMAIL غير صحيح")
            return False
        
        # فحص Database URL
        if not database_url or not database_url.startswith('https://'):
            print("❌ FIREBASE_DATABASE_URL غير صحيح")
            return False
        
        print("✅ إعدادات Firebase تبدو صحيحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص إعدادات Firebase: {e}")
        return False

def check_dependencies():
    """فحص المتطلبات"""
    print("\n📦 فحص المتطلبات...")
    
    try:
        import flask
        print(f"✅ Flask {flask.__version__}")
    except ImportError:
        print("❌ Flask غير مثبت")
        return False
    
    try:
        import firebase_admin
        print(f"✅ Firebase Admin SDK مثبت")
    except ImportError:
        print("❌ Firebase Admin SDK غير مثبت")
        return False
    
    try:
        import dotenv
        print(f"✅ python-dotenv مثبت")
    except ImportError:
        print("❌ python-dotenv غير مثبت")
        return False
    
    return True

def check_firebase_connection():
    """فحص الاتصال بـ Firebase"""
    print("\n🌐 فحص الاتصال بـ Firebase...")
    
    try:
        import firebase_admin
        from firebase_admin import credentials, db
        
        # إعداد Firebase
        firebase_config = {
            "type": "service_account",
            "project_id": os.getenv('FIREBASE_PROJECT_ID'),
            "private_key_id": os.getenv('FIREBASE_PRIVATE_KEY_ID'),
            "private_key": os.getenv('FIREBASE_PRIVATE_KEY', '').replace('\\n', '\n'),
            "client_email": os.getenv('FIREBASE_CLIENT_EMAIL'),
            "client_id": os.getenv('FIREBASE_CLIENT_ID'),
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
        }
        
        # محاولة الاتصال
        if not firebase_admin._apps:
            cred = credentials.Certificate(firebase_config)
            firebase_admin.initialize_app(cred, {
                'databaseURL': os.getenv('FIREBASE_DATABASE_URL')
            })
        
        # اختبار قراءة البيانات
        ref = db.reference('/')
        ref.get()
        
        print("✅ الاتصال بـ Firebase ناجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاتصال بـ Firebase: {e}")
        return False

def check_port():
    """فحص المنفذ"""
    print("\n🔌 فحص المنفذ...")
    
    import socket
    
    port = int(os.getenv('PORT', 21226))
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"⚠️ المنفذ {port} مستخدم بالفعل")
            return False
        else:
            print(f"✅ المنفذ {port} متاح")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص المنفذ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص إعدادات QR Contract Viewer")
    print("==================================")
    
    checks = [
        check_python_version,
        check_env_file,
        check_firebase_config,
        check_dependencies,
        check_firebase_connection,
        check_port
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
    
    print(f"\n📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الفحوصات نجحت! التطبيق جاهز للتشغيل")
        print("\nلتشغيل التطبيق:")
        print("python run.py")
        return True
    else:
        print("❌ بعض الفحوصات فشلت. يرجى إصلاح المشاكل أولاً")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
