#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سيرفر QR بطريقة بسيطة
"""

import os
import sys

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_data_file():
    """التحقق من وجود ملف البيانات"""
    data_file = os.path.join(os.path.dirname(__file__), '..', 'contracts_data.json')
    if not os.path.exists(data_file):
        print(f"تحذير: ملف البيانات غير موجود: {data_file}")
        print("سيتم إنشاء ملف بيانات تجريبي...")

        # إنشاء ملف بيانات تجريبي
        import json
        sample_data = {
            "finalized_contracts": {
                "2024": {
                    "contract_001": {
                        "uuid": "12345678-1234-1234-1234-123456789abc",
                        "serial_number": "001",
                        "date": "2024-01-15",
                        "time": "10:30",
                        "seller_name": "أحمد محمد علي",
                        "seller_id": "123456789",
                        "buyer_name": "فاطمة حسن محمود",
                        "buyer_id": "987654321",
                        "car_make": "تويوتا",
                        "car_model": "كامري",
                        "car_year": "2020",
                        "car_color": "أبيض",
                        "chassis_number": "ABC123456789",
                        "sale_amount": "25000",
                        "paid_amount": "20000",
                        "currency": "USD",
                        "created_at": "2024-01-15T10:30:00",
                        "computer_name": "PC-001"
                    }
                }
            }
        }

        try:
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, ensure_ascii=False, indent=2)
            print(f"تم إنشاء ملف البيانات: {data_file}")
        except Exception as e:
            print(f"خطأ في إنشاء ملف البيانات: {e}")
            return False

    return True

if __name__ == '__main__':
    try:
        # التحقق من ملف البيانات
        if not check_data_file():
            input("اضغط Enter للخروج...")
            sys.exit(1)

        # استيراد التطبيق
        from app import app, logger

        # تشغيل السيرفر على المنفذ 21226
        port = 21226
        print("=" * 50)
        print("        سيرفر QR للعقود")
        print("=" * 50)
        logger.info(f"بدء تشغيل سيرفر QR على المنفذ {port}")
        print(f"للوصول للسيرفر: http://localhost:{port}")
        print(f"لعرض عقد تجريبي: http://localhost:{port}/contract/12345678-1234-1234-1234-123456789abc")
        print("لإيقاف السيرفر: اضغط Ctrl+C")
        print("=" * 50)

        app.run(host='0.0.0.0', port=port, debug=True)
    except KeyboardInterrupt:
        print("\nتم إيقاف السيرفر بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ في تشغيل السيرفر: {e}")
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")
