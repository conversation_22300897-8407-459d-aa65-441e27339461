# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Application specific
uploads/*.png
uploads/*.jpg
uploads/*.jpeg
filled_template.docx
serial_number.txt

# Cache files
.cache/
.local/

# Temporary files
~$*
*.tmp
*.temp

# Logs
*.log
