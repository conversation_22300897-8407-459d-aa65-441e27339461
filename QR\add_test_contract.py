#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عقد تجريبي إلى Firebase Realtime Database
"""

import os
import sys
import uuid
import json
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(__file__))

def add_test_contract():
    """إضافة عقد تجريبي إلى Firebase"""
    try:
        # تهيئة Firebase
        import firebase_admin
        from firebase_admin import credentials, db
        from dotenv import load_dotenv
        
        # تحميل متغيرات البيئة
        load_dotenv()
        
        # التحقق من وجود تطبيق Firebase مُهيأ مسبقاً
        if not firebase_admin._apps:
            # إنشاء credentials من متغيرات البيئة
            cred_dict = {
                "type": os.getenv('FIREBASE_TYPE'),
                "project_id": os.getenv('FIREBASE_PROJECT_ID'),
                "private_key_id": os.getenv('FIREBASE_PRIVATE_KEY_ID'),
                "private_key": os.getenv('FIREBASE_PRIVATE_KEY').replace('\\n', '\n'),
                "client_email": os.getenv('FIREBASE_CLIENT_EMAIL'),
                "client_id": os.getenv('FIREBASE_CLIENT_ID'),
                "auth_uri": os.getenv('FIREBASE_AUTH_URI'),
                "token_uri": os.getenv('FIREBASE_TOKEN_URI'),
                "auth_provider_x509_cert_url": os.getenv('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
                "client_x509_cert_url": os.getenv('FIREBASE_CLIENT_X509_CERT_URL'),
                "universe_domain": os.getenv('FIREBASE_UNIVERSE_DOMAIN', 'googleapis.com')
            }
            
            # تهيئة Firebase Admin
            cred = credentials.Certificate(cred_dict)
            firebase_admin.initialize_app(cred, {
                'databaseURL': 'https://car-report-a01fe-default-rtdb.firebaseio.com'
            })
            print("✅ تم تهيئة Firebase بنجاح")
        
        # إنشاء UUID للعقد التجريبي
        contract_uuid = str(uuid.uuid4())
        
        # بيانات العقد التجريبي
        contract_data = {
            "uuid": contract_uuid,
            "serial_number": "TEST-001",
            "date": "2024-12-18",
            "time": "14:30",
            "seller_name": "أحمد محمد علي",
            "seller_id": "123456789",
            "buyer_name": "فاطمة حسن محمود",
            "buyer_id": "987654321",
            "car_make": "تويوتا",
            "car_model": "كامري",
            "car_year": "2020",
            "car_color": "أبيض",
            "chassis_number": "ABC123456789",
            "sale_amount": "25000",
            "paid_amount": "20000",
            "remaining_amount": "5000",
            "currency": "USD",
            "amount_in_words": "خمسة وعشرون ألف دولار أمريكي",
            "created_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "computer_name": "TEST-PC"
        }
        
        # إنشاء معرف العقد
        contract_id = f"contract_TEST-001_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # بنية البيانات كما هي في المشروع الأصلي
        contract_with_metadata = {
            'contract_data': contract_data,
            'images': {},
            'status': 'finalized',
            'finalized_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'word_filename': f'test_contract_{contract_id}.docx',
            'pdf_filename': f'test_contract_{contract_id}.pdf',
            'pdf_converted': True,
            'uuid': contract_uuid
        }
        
        # حفظ العقد في Firebase
        ref = db.reference(f'finalized_contracts/{contract_id}')
        ref.set(contract_with_metadata)
        
        print("🎉 تم إضافة العقد التجريبي بنجاح!")
        print(f"UUID: {contract_uuid}")
        print(f"Contract ID: {contract_id}")
        print(f"رابط العرض: https://425243.site.bot-hosting.net/contract/{contract_uuid}")
        print(f"رابط محلي: http://localhost:21226/contract/{contract_uuid}")
        
        return contract_uuid, contract_id
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العقد: {e}")
        return None, None

def list_contracts():
    """عرض جميع العقود في Firebase"""
    try:
        import firebase_admin
        from firebase_admin import credentials, db
        from dotenv import load_dotenv

        # تحميل متغيرات البيئة
        load_dotenv()

        # التحقق من وجود تطبيق Firebase مُهيأ مسبقاً
        if not firebase_admin._apps:
            # إنشاء credentials من متغيرات البيئة
            cred_dict = {
                "type": os.getenv('FIREBASE_TYPE'),
                "project_id": os.getenv('FIREBASE_PROJECT_ID'),
                "private_key_id": os.getenv('FIREBASE_PRIVATE_KEY_ID'),
                "private_key": os.getenv('FIREBASE_PRIVATE_KEY').replace('\\n', '\n'),
                "client_email": os.getenv('FIREBASE_CLIENT_EMAIL'),
                "client_id": os.getenv('FIREBASE_CLIENT_ID'),
                "auth_uri": os.getenv('FIREBASE_AUTH_URI'),
                "token_uri": os.getenv('FIREBASE_TOKEN_URI'),
                "auth_provider_x509_cert_url": os.getenv('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
                "client_x509_cert_url": os.getenv('FIREBASE_CLIENT_X509_CERT_URL'),
                "universe_domain": os.getenv('FIREBASE_UNIVERSE_DOMAIN', 'googleapis.com')
            }

            # تهيئة Firebase Admin
            cred = credentials.Certificate(cred_dict)
            firebase_admin.initialize_app(cred, {
                'databaseURL': 'https://car-report-a01fe-default-rtdb.firebaseio.com'
            })
            print("✅ تم تهيئة Firebase بنجاح")
        
        ref = db.reference('finalized_contracts')
        contracts = ref.get()
        
        if not contracts:
            print("📝 لا توجد عقود في Firebase")
            return
        
        print("📋 العقود الموجودة في Firebase:")
        print("=" * 50)
        
        for contract_id, contract_data in contracts.items():
            uuid_val = contract_data.get('uuid', 'غير محدد')
            serial = contract_data.get('contract_data', {}).get('serial_number', 'غير محدد')
            finalized_at = contract_data.get('finalized_at', 'غير محدد')
            
            print(f"Contract ID: {contract_id}")
            print(f"UUID: {uuid_val}")
            print(f"Serial: {serial}")
            print(f"تاريخ الإبرام: {finalized_at}")
            print(f"رابط العرض: https://425243.site.bot-hosting.net/contract/{uuid_val}")
            print("-" * 30)
        
    except Exception as e:
        print(f"❌ خطأ في عرض العقود: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "list":
        list_contracts()
    else:
        add_test_contract()
