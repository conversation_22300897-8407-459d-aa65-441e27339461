# سيرفر QR للعقود - النسخة المبسطة

## الوصف
سيرفر Flask بسيط لعرض العقود عند قراءة رمز QR، بدون Firebase وبحماية بسيطة.

## المتطلبات
- Python 3.7+
- Flask

## التثبيت والتشغيل

### الطريقة الأولى: استخدام ملف batch (Windows)
```bash
# انقر مرتين على الملف
start_simple.bat
```

### الطريقة الثانية: التشغيل اليدوي
```bash
# تثبيت Flask
pip install flask

# تشغيل السيرفر
python app.py
```

## الوصول للسيرفر
- الصفحة الرئيسية: http://localhost:21226
- عقد تجريبي: http://localhost:21226/contract/12345678-1234-1234-1234-123456789abc

## ملف البيانات
- يتم قراءة العقود من ملف `contracts_data.json` في المجلد الرئيسي
- يمكن تعديل هذا الملف لإضافة عقود جديدة

## هيكل البيانات
```json
{
  "finalized_contracts": {
    "2024": {
      "contract_001": {
        "uuid": "12345678-1234-1234-1234-123456789abc",
        "serial_number": "001",
        "date": "2024-01-15",
        "seller_name": "اسم البائع",
        "buyer_name": "اسم المشتري",
        ...
      }
    }
  }
}
```

## الحماية
- Rate limiting للطلبات
- التحقق من صحة UUID
- منع الوصول المباشر للملفات
- Headers أمان إضافية

## الملفات المهمة
- `app.py` - التطبيق الرئيسي
- `run_simple.py` - ملف التشغيل المبسط
- `start_simple.bat` - ملف التشغيل لـ Windows
- `contracts_data.json` - ملف البيانات
- `templates/` - قوالب HTML

## ملاحظات
- لا يحتاج Docker أو Firebase
- يعمل محلياً فقط
- البيانات محفوظة في ملف JSON محلي
- يمكن تشغيله على أي كمبيوتر بـ Python
