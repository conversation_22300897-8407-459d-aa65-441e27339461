#!/bin/bash

# QR Contract Viewer - Start Script
# =================================

echo "🚀 بدء تشغيل QR Contract Viewer"
echo "==============================="

# التحقق من وجود البيئة الافتراضية
if [ ! -d "venv" ]; then
    echo "❌ البيئة الافتراضية غير موجودة!"
    echo "يرجى تشغيل install.sh أولاً"
    exit 1
fi

# التحقق من وجود ملف .env
if [ ! -f ".env" ]; then
    echo "❌ ملف .env غير موجود!"
    echo "يرجى نسخ .env.example إلى .env وتعديل القيم"
    exit 1
fi

# تفعيل البيئة الافتراضية
echo "🔧 تفعيل البيئة الافتراضية..."
source venv/bin/activate

# تحديد وضع التشغيل
MODE=${1:-production}

if [ "$MODE" = "dev" ] || [ "$MODE" = "development" ]; then
    echo "🛠️ تشغيل في وضع التطوير..."
    export FLASK_ENV=development
    python run.py
elif [ "$MODE" = "gunicorn" ]; then
    echo "🏭 تشغيل مع Gunicorn..."
    gunicorn wsgi:application --bind 0.0.0.0:21226 --workers 4 --timeout 120 --reload
else
    echo "🏭 تشغيل في وضع الإنتاج..."
    python run.py
fi
