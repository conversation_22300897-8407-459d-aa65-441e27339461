<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول الحاسوب - مكتب علي العبدلي</title>
    <style>
        body {
            font-family: 'Traditional Arabic', Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .login-container {
            background: white;
            padding: 45px;
            border-radius: 12px;
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
            max-width: 420px;
            width: 100%;
            text-align: center;
            border: 1px solid #e8e9ea;
        }
        
        .logo {
            margin-bottom: 30px;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 30px;
            font-weight: 700;
            margin-bottom: 12px;
        }

        h2 {
            color: #6c757d;
            font-size: 18px;
            margin-bottom: 35px;
            font-weight: 500;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }
        
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 14px;
            border: 1px solid #d1d9e0;
            border-radius: 8px;
            font-size: 16px;
            direction: rtl;
            text-align: right;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        .error {
            background-color: #ffe6e6;
            color: #d00;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #ffcccc;
        }
        
        .info {
            background-color: #e6f3ff;
            color: #0066cc;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            border: 1px solid #cce6ff;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .computer-icon {
            font-size: 52px;
            color: #007bff;
            margin-bottom: 25px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="computer-icon">💻</div>
        <h1>مكتب علي العبدلي</h1>
        <h2>تسجيل دخول الحاسوب</h2>
        
        {% if error %}
        <div class="error">{{ error }}</div>
        {% endif %}
        
        <form method="POST">
            <div class="form-group">
                <label for="computer_name">اسم الحاسوب:</label>
                <input type="text" id="computer_name" name="computer_name" required 
                       placeholder="أدخل اسم فريد للحاسوب" maxlength="50">
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required 
                       placeholder="أدخل كلمة المرور" maxlength="50">
            </div>
            
            <button type="submit" class="login-btn">دخول</button>
        </form>
        
        <div class="info">
            <strong>ملاحظة:</strong><br>
            • إذا كان هذا أول استخدام للحاسوب، سيتم إنشاء حساب جديد<br>
            • إذا كان الحاسوب مسجل مسبقاً، أدخل كلمة المرور الصحيحة<br>
            • اسم الحاسوب يجب أن يكون فريد ولا يمكن تكراره
        </div>
    </div>
</body>
</html>
