@echo off
chcp 65001 >nul

echo 🚀 بدء تشغيل QR Contract Viewer
echo ===============================

REM التحقق من وجود البيئة الافتراضية
if not exist "venv" (
    echo ❌ البيئة الافتراضية غير موجودة!
    echo يرجى تشغيل install.bat أولاً
    pause
    exit /b 1
)

REM التحقق من وجود ملف .env
if not exist ".env" (
    echo ❌ ملف .env غير موجود!
    echo يرجى نسخ .env.example إلى .env وتعديل القيم
    pause
    exit /b 1
)

REM تفعيل البيئة الافتراضية
echo 🔧 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

REM تحديد وضع التشغيل
set MODE=%1
if "%MODE%"=="" set MODE=production

if "%MODE%"=="dev" (
    echo 🛠️ تشغيل في وضع التطوير...
    set FLASK_ENV=development
    python run.py
) else if "%MODE%"=="development" (
    echo 🛠️ تشغيل في وضع التطوير...
    set FLASK_ENV=development
    python run.py
) else (
    echo 🏭 تشغيل في وضع الإنتاج...
    python run.py
)

pause
