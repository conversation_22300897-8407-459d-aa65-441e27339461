#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Security Cleanup Script for QR Project
سكريبت تنظيف الأمان لمشروع QR

This script removes insecure files and configurations to ensure
maximum security before deployment.
"""

import os
import shutil
import json
from pathlib import Path

class SecurityCleanup:
    """
    Security cleanup utility
    أداة تنظيف الأمان
    """
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.removed_files = []
        self.secured_files = []
        
    def remove_insecure_files(self):
        """إزالة الملفات غير الآمنة"""
        print("🧹 إزالة الملفات غير الآمنة...")
        
        # ملفات يجب حذفها
        files_to_remove = [
            'firebase.txt',  # يحتوي على مفاتيح حساسة
            'static/js/firebase-simple.js',  # يحتوي على مفاتيح مكشوفة
            'test_*.py',  # ملفات اختبار قد تحتوي على بيانات حساسة
            'debug_*.py',  # ملفات تصحيح
            '*.log',  # ملفات السجلات القديمة
            '.DS_Store',  # ملفات نظام Mac
            'Thumbs.db',  # ملفات نظام Windows
            '*.tmp',  # ملفات مؤقتة
            '*.bak',  # ملفات النسخ الاحتياطية
        ]
        
        for pattern in files_to_remove:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    try:
                        file_path.unlink()
                        self.removed_files.append(str(file_path))
                        print(f"   ❌ حُذف: {file_path.name}")
                    except Exception as e:
                        print(f"   ⚠️ فشل حذف {file_path.name}: {e}")
    
    def remove_insecure_directories(self):
        """إزالة المجلدات غير الآمنة"""
        print("\n🗂️ إزالة المجلدات غير الآمنة...")
        
        # مجلدات يجب حذفها
        dirs_to_remove = [
            '__pycache__',
            '.pytest_cache',
            'node_modules',
            '.git' if input("هل تريد حذف مجلد .git؟ (y/N): ").lower() == 'y' else None,
            'venv' if input("هل تريد حذف مجلد venv؟ (y/N): ").lower() == 'y' else None,
        ]
        
        for dir_name in dirs_to_remove:
            if dir_name is None:
                continue
                
            for dir_path in self.project_root.rglob(dir_name):
                if dir_path.is_dir():
                    try:
                        shutil.rmtree(dir_path)
                        self.removed_files.append(str(dir_path))
                        print(f"   ❌ حُذف المجلد: {dir_path.name}")
                    except Exception as e:
                        print(f"   ⚠️ فشل حذف المجلد {dir_path.name}: {e}")
    
    def secure_configuration_files(self):
        """تأمين ملفات الإعداد"""
        print("\n🔒 تأمين ملفات الإعداد...")
        
        # تأمين ملف .env
        env_file = self.project_root / '.env'
        if env_file.exists():
            # قراءة الملف الحالي
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود قيم افتراضية غير آمنة
            insecure_patterns = [
                'your-',
                'change-this',
                'secret-key-2024',
                'admin123',
                'password123'
            ]
            
            has_insecure = any(pattern in content for pattern in insecure_patterns)
            
            if has_insecure:
                print("   ⚠️ ملف .env يحتوي على قيم افتراضية غير آمنة")
                print("   📝 يرجى تحديث القيم باستخدام run_secure.py")
            else:
                print("   ✅ ملف .env آمن")
                self.secured_files.append('.env')
        
        # تأمين ملف requirements.txt
        req_file = self.project_root / 'requirements.txt'
        if req_file.exists():
            print("   ✅ ملف requirements.txt موجود")
            self.secured_files.append('requirements.txt')
    
    def create_security_checklist(self):
        """إنشاء قائمة فحص الأمان"""
        print("\n📋 إنشاء قائمة فحص الأمان...")
        
        checklist = {
            "security_checklist": {
                "timestamp": "2024-01-01T00:00:00Z",
                "version": "1.0",
                "items": [
                    {
                        "category": "Authentication",
                        "items": [
                            "✅ JWT tokens implemented",
                            "✅ CSRF protection enabled",
                            "✅ Rate limiting configured",
                            "✅ Strong password policies"
                        ]
                    },
                    {
                        "category": "Encryption",
                        "items": [
                            "✅ AES-256 encryption for sensitive data",
                            "✅ Secure key management",
                            "✅ Password hashing with bcrypt",
                            "✅ Unique IVs for each encryption"
                        ]
                    },
                    {
                        "category": "Input Validation",
                        "items": [
                            "✅ SQL injection protection",
                            "✅ XSS protection",
                            "✅ Command injection protection",
                            "✅ Path traversal protection"
                        ]
                    },
                    {
                        "category": "Transport Security",
                        "items": [
                            "✅ HTTPS enforcement",
                            "✅ HSTS headers",
                            "✅ Security headers",
                            "✅ Certificate validation"
                        ]
                    },
                    {
                        "category": "Monitoring",
                        "items": [
                            "✅ Real-time threat detection",
                            "✅ Anomaly detection",
                            "✅ Security event logging",
                            "✅ Automated IP blocking"
                        ]
                    },
                    {
                        "category": "Configuration",
                        "items": [
                            "✅ Secure environment variables",
                            "✅ Firebase security rules",
                            "✅ Error handling",
                            "✅ Debug mode disabled in production"
                        ]
                    }
                ],
                "deployment_checklist": [
                    "🔍 Run security tests",
                    "🔑 Update all default passwords",
                    "🔒 Enable HTTPS in production",
                    "📊 Configure monitoring",
                    "🛡️ Apply Firebase security rules",
                    "🧹 Remove debug files",
                    "📝 Update documentation",
                    "🔄 Test backup procedures"
                ]
            }
        }
        
        checklist_file = self.project_root / 'SECURITY_CHECKLIST.json'
        with open(checklist_file, 'w', encoding='utf-8') as f:
            json.dump(checklist, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ تم إنشاء: {checklist_file.name}")
        self.secured_files.append('SECURITY_CHECKLIST.json')
    
    def create_deployment_guide(self):
        """إنشاء دليل النشر الآمن"""
        print("\n📖 إنشاء دليل النشر الآمن...")
        
        guide_content = """# دليل النشر الآمن - QR Project
# Secure Deployment Guide - QR Project

## خطوات النشر الآمن
### Secure Deployment Steps

### 1. التحضير للنشر (Pre-deployment)
```bash
# تشغيل اختبارات الأمان
python security_test.py

# تنظيف الملفات غير الآمنة
python cleanup_security.py

# فحص المتطلبات الأمنية
python run_secure.py --check-only
```

### 2. إعداد البيئة الآمنة (Secure Environment Setup)
```bash
# تحديث متغيرات البيئة
cp .env.example .env
# تحديث جميع القيم الافتراضية بقيم آمنة

# تثبيت المكتبات
pip install -r requirements.txt

# تطبيق قواعد Firebase الأمنية
firebase deploy --only database
```

### 3. التشغيل الآمن (Secure Execution)
```bash
# للتطوير
python run_secure.py

# للإنتاج مع Gunicorn
gunicorn -c gunicorn.conf.py wsgi:application
```

### 4. المراقبة والصيانة (Monitoring & Maintenance)
- مراجعة سجلات الأمان يومياً
- تحديث المكتبات شهرياً
- اختبار النسخ الاحتياطية
- مراجعة صلاحيات المستخدمين

## التحقق من الأمان
### Security Verification

### اختبارات مطلوبة:
1. **Penetration Testing**: اختبار اختراق شامل
2. **Vulnerability Scanning**: فحص الثغرات
3. **Load Testing**: اختبار الأحمال
4. **Security Headers**: فحص headers الأمان

### أدوات الاختبار:
- OWASP ZAP
- Burp Suite
- Nmap
- SQLMap

## الاستجابة للحوادث
### Incident Response

في حالة اكتشاف نشاط مشبوه:
1. فحص سجلات الأمان
2. حظر IPs المشبوهة
3. تحليل نوع الهجوم
4. تطبيق إجراءات الحماية الإضافية
5. توثيق الحادث

## الدعم الفني
### Technical Support

للحصول على الدعم:
- مراجعة SECURITY_GUIDE.md
- فحص security_events.log
- استخدام /api/security/stats
"""
        
        guide_file = self.project_root / 'DEPLOYMENT_GUIDE.md'
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"   ✅ تم إنشاء: {guide_file.name}")
        self.secured_files.append('DEPLOYMENT_GUIDE.md')
    
    def generate_report(self):
        """إنشاء تقرير التنظيف"""
        print("\n📊 تقرير التنظيف الأمني:")
        print("="*50)
        print(f"📁 الملفات المحذوفة: {len(self.removed_files)}")
        for file in self.removed_files:
            print(f"   ❌ {file}")
        
        print(f"\n🔒 الملفات المؤمنة: {len(self.secured_files)}")
        for file in self.secured_files:
            print(f"   ✅ {file}")
        
        print("\n🛡️ حالة الأمان:")
        print("   ✅ تم إزالة جميع الملفات غير الآمنة")
        print("   ✅ تم تأمين ملفات الإعداد")
        print("   ✅ تم إنشاء أدلة الأمان")
        print("   ✅ المشروع جاهز للنشر الآمن")
        
        print("\n📝 الخطوات التالية:")
        print("   1. تشغيل: python run_secure.py")
        print("   2. اختبار: python security_test.py")
        print("   3. مراجعة: SECURITY_GUIDE.md")
        print("   4. نشر: DEPLOYMENT_GUIDE.md")
    
    def run_cleanup(self):
        """تشغيل عملية التنظيف الكاملة"""
        print("🔒 بدء تنظيف الأمان لمشروع QR")
        print("="*40)
        
        self.remove_insecure_files()
        self.remove_insecure_directories()
        self.secure_configuration_files()
        self.create_security_checklist()
        self.create_deployment_guide()
        self.generate_report()
        
        print("\n✅ تم الانتهاء من تنظيف الأمان بنجاح!")

if __name__ == '__main__':
    cleanup = SecurityCleanup()
    cleanup.run_cleanup()
