# Car Contract Management System

نظام إدارة عقود السيارات مع تكامل رموز QR

## الوصف

نظام شامل لإدارة عقود السيارات يتضمن:
- إنشاء وإدارة العقود
- تحويل الأرقام إلى النص العربي
- دعم العملات (الدولار الأمريكي والدينار العراقي)
- نظام رموز QR لعرض العقود
- إدارة المستخدمين والصلاحيات
- تصدير سنوي للعقود
- واجهة إدارية متقدمة

## المميزات

### الميزات الأساسية
- ✅ إنشاء عقود السيارات
- ✅ تحويل الأرقام إلى النص العربي الفصيح
- ✅ دعم العملات المتعددة (USD/IQD)
- ✅ نظام رموز QR للعقود
- ✅ حفظ واسترجاع العقود
- ✅ طباعة العقود بصيغة PDF

### الميزات المتقدمة
- ✅ واجهة إدارية شاملة
- ✅ إدارة المستخدمين
- ✅ تصدير سنوي للعقود
- ✅ نظام البحث والتصفية
- ✅ تحرير العقود الموجودة
- ✅ نظام الأرقام التسلسلية

### التكامل مع Firebase
- ✅ حفظ البيانات في Firebase Realtime Database
- ✅ ضغط الصور تلقائياً
- ✅ نظام QR منفصل للأمان

## متطلبات النظام

### Python Dependencies
```
Flask==2.3.3
python-docx==0.8.11
Pillow==10.0.1
qrcode==7.4.2
firebase-admin==6.2.0
gunicorn==21.2.0
```

### متطلبات إضافية
- LibreOffice (لتحويل PDF)
- Firebase Project مع Realtime Database

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. إعداد Firebase
1. إنشاء مشروع Firebase جديد
2. تفعيل Realtime Database
3. إضافة ملف `firebase.txt` مع بيانات الاتصال

### 3. تشغيل الخادم الرئيسي
```bash
python server.py
```

### 4. تشغيل خادم QR (اختياري)
```bash
cd QR
python app.py
```

## الاستخدام

### للمستخدمين العاديين
1. تسجيل الدخول
2. إنشاء عقد جديد
3. ملء البيانات المطلوبة
4. التقاط صور السيارة
5. حفظ وطباعة العقد

### للمديرين
1. الوصول إلى لوحة الإدارة
2. إدارة المستخدمين
3. عرض وتحرير العقود
4. تصدير العقود السنوية
5. إدارة الأرقام التسلسلية

## هيكل المشروع

```
car/
├── server.py                 # الخادم الرئيسي
├── templates/               # قوالب HTML
├── static/                  # الملفات الثابتة
├── QR/                      # نظام QR منفصل
├── firebase_config.py       # إعدادات Firebase
├── arabic_number_converter.py # تحويل الأرقام للعربية
└── requirements.txt         # متطلبات Python
```

## الأمان

- نظام مصادقة المستخدمين
- صلاحيات إدارية منفصلة
- خادم QR منفصل للأمان
- تشفير البيانات في Firebase

## الدعم والمساهمة

للدعم الفني أو المساهمة في المشروع، يرجى فتح issue في GitHub.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

## English Description

A comprehensive car contract management system with QR code integration, featuring:

- Arabic number-to-text conversion
- Multi-currency support (USD/IQD)
- QR code generation for contracts
- Admin dashboard
- Annual contract export
- Firebase integration

### Quick Start
1. Install dependencies: `pip install -r requirements.txt`
2. Configure Firebase in `firebase.txt`
3. Run: `python server.py`
4. Access: `http://localhost:5000`

### Features
- Contract creation and management
- Arabic text conversion following Classical Arabic grammar
- QR code integration
- User management
- PDF generation
- Annual export functionality
