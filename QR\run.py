#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Contract Viewer - Production Runner
=====================================

ملف تشغيل التطبيق للإنتاج
"""

import os
import sys
from app import app, init_firebase

def main():
    """تشغيل التطبيق"""
    try:
        # التحقق من وجود ملف .env
        if not os.path.exists('.env'):
            print("❌ ملف .env غير موجود!")
            print("📝 يرجى نسخ .env.example إلى .env وتعديل القيم")
            print("cp .env.example .env")
            sys.exit(1)
        
        # تهيئة Firebase
        print("🔥 تهيئة Firebase...")
        init_firebase()
        print("✅ تم تهيئة Firebase بنجاح")
        
        # إعداد السيرفر
        port = int(os.environ.get('PORT', 21226))
        debug = os.environ.get('FLASK_ENV') == 'development'
        
        print(f"🚀 تشغيل السيرفر على المنفذ {port}")
        print(f"🌐 الرابط: http://localhost:{port}")
        print(f"🔧 وضع التطوير: {'مفعل' if debug else 'معطل'}")
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=port,
            debug=debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف السيرفر")
    except Exception as e:
        print(f"❌ خطأ في تشغيل السيرفر: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
