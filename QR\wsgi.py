#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WSGI Entry Point for QR Contract Viewer
=======================================

نقطة دخول WSGI للنشر على الخوادم مثل Gunicorn
"""

import os
import sys
from app import app, init_firebase

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(__file__))

# تهيئة Firebase عند بدء التطبيق
try:
    init_firebase()
    print("✅ تم تهيئة Firebase بنجاح")
except Exception as e:
    print(f"❌ خطأ في تهيئة Firebase: {e}")
    raise

# تصدير التطبيق للـ WSGI server
application = app

if __name__ == "__main__":
    # للتشغيل المباشر
    port = int(os.environ.get('PORT', 21226))
    app.run(host='0.0.0.0', port=port)
