<!-- شريط الأدوات العلوي -->
<div class="custom-toolbar">
    <div class="toolbar-container">
        <!-- الجانب الأيمن - معلومات المستخدم -->
        <div class="toolbar-right">
            <div class="user-info" onclick="toggleUserMenu()">
                <div class="user-avatar">
                    {% if session.get('is_admin') %}
                        <span class="admin-icon">👑</span>
                    {% else %}
                        <span class="user-icon">👤</span>
                    {% endif %}
                </div>
                <div class="user-details">
                    <span class="user-name">{{ session.get('computer_name', 'مستخدم') }}</span>
                    <span class="user-type">
                        {% if session.get('is_admin') %}
                            إداري
                        {% else %}
                            مستخدم عادي
                        {% endif %}
                    </span>
                </div>
                <div class="dropdown-arrow">▼</div>
            </div>
            
            <!-- القائمة المنسدلة -->
            <div class="user-dropdown" id="userDropdown">
                {% if session.get('is_admin') %}
                    <a href="/admin/dashboard" class="dropdown-item">
                        <span class="item-icon">🎛️</span>
                        لوحة التحكم
                    </a>
                    <a href="/computers" class="dropdown-item">
                        <span class="item-icon">💻</span>
                        قائمة الحاسبات
                    </a>
                    <a href="/admin/annual_export" class="dropdown-item">
                        <span class="item-icon">📊</span>
                        تحميل عقود السنة
                    </a>
                    <div class="dropdown-divider"></div>
                {% endif %}
                <a href="/" class="dropdown-item">
                    <span class="item-icon">📝</span>
                    إنشاء عقد جديد
                </a>
                <a href="/contracts" class="dropdown-item">
                    <span class="item-icon">📋</span>
                    عرض العقود المبرمة
                </a>
                <div class="dropdown-divider"></div>
                <a href="/logout" class="dropdown-item logout-item">
                    <span class="item-icon">🚪</span>
                    تسجيل خروج
                </a>
            </div>
        </div>
        
        <!-- الجانب الأيسر - شعار المكتب -->
        <div class="toolbar-left">
            <a href="/" class="office-logo">
                <span class="logo-icon">🏢</span>
                <span class="office-name">مكتب دبي</span>
            </a>
        </div>
    </div>
</div>

<style>
.custom-toolbar {
    background: #ffffff;
    color: #2c3e50;
    padding: 0;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid #e8e9ea;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
    left: 0;
    right: 0;
}

.toolbar-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    height: 70px;
    width: 100%;
    box-sizing: border-box;
}

.toolbar-right {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px 18px;
    border-radius: 30px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

.user-info:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.user-avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    font-size: 18px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.15);
    border: 2px solid #ffffff;
}

.admin-icon {
    color: #ffd700;
    filter: drop-shadow(0 0 4px rgba(255,215,0,0.6));
}

.user-icon {
    color: white;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-left: 10px;
}

.user-name {
    font-weight: 600;
    font-size: 15px;
    color: #2c3e50;
}

.user-type {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

.dropdown-arrow {
    margin-left: 10px;
    font-size: 11px;
    transition: transform 0.3s ease;
    color: #6c757d;
}

.user-info.active .dropdown-arrow {
    transform: rotate(180deg);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 12px 35px rgba(0,0,0,0.1);
    min-width: 260px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #dee2e6;
    margin-top: 10px;
    overflow: hidden;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid #f1f3f4;
    font-weight: 500;
    font-size: 14px;
    position: relative;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #2c3e50;
    transform: translateX(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.dropdown-item:last-child {
    border-bottom: none;
    border-radius: 0 0 12px 12px;
}

.dropdown-item:first-child {
    border-radius: 12px 12px 0 0;
}

.logout-item {
    border-top: 1px solid #e9ecef;
    margin-top: 4px;
}

.logout-item:hover {
    background: linear-gradient(135deg, #f1f3f4, #e8eaed);
    color: #2c3e50;
}

.item-icon {
    margin-left: 14px;
    font-size: 16px;
    width: 24px;
    text-align: center;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.dropdown-item:hover .item-icon {
    opacity: 1;
}

.dropdown-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #e9ecef, transparent);
    margin: 6px 0;
}

.toolbar-left {
    display: flex;
    align-items: center;
}

.office-logo {
    display: flex;
    align-items: center;
    font-weight: 600;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.office-logo:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-decoration: none;
    color: inherit;
}

.logo-icon {
    font-size: 26px;
    margin-left: 12px;
    color: #007bff;
}

.office-name {
    font-size: 20px;
    color: #2c3e50;
    font-weight: 600;
}

/* تأثيرات متجاوبة */
@media (max-width: 768px) {
    .toolbar-container {
        padding: 0 20px;
        height: 65px;
    }

    .office-name {
        font-size: 18px;
    }

    .user-details {
        display: none;
    }

    .user-dropdown {
        min-width: 220px;
        right: -10px;
    }
}

@media (max-width: 480px) {
    .toolbar-container {
        padding: 0 15px;
    }

    .logo-icon {
        font-size: 22px;
    }

    .office-name {
        font-size: 16px;
    }
}
</style>

<script>
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    const userInfo = document.querySelector('.user-info');
    
    dropdown.classList.toggle('show');
    userInfo.classList.toggle('active');
}

// إغلاق القائمة عند النقر خارجها
document.addEventListener('click', function(event) {
    const userInfo = document.querySelector('.user-info');
    const dropdown = document.getElementById('userDropdown');
    
    if (!userInfo.contains(event.target)) {
        dropdown.classList.remove('show');
        userInfo.classList.remove('active');
    }
});

// إغلاق القائمة عند الضغط على Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const dropdown = document.getElementById('userDropdown');
        const userInfo = document.querySelector('.user-info');
        dropdown.classList.remove('show');
        userInfo.classList.remove('active');
    }
});
</script>
