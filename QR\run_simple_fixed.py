#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Application - Simple Fixed Version
تطبيق QR - نسخة بسيطة تعمل

Simple version that works without excessive security blocking.
"""

import os
import sys
import secrets
from dotenv import load_dotenv

def main():
    """تشغيل التطبيق البسيط"""
    try:
        print("QR Application - Simple Version")
        print("=" * 40)
        
        # تحميل متغيرات البيئة
        load_dotenv()
        
        # تحديث المفاتيح الأمنية إذا لزم الأمر
        if not os.getenv('JWT_SECRET_KEY') or 'your-' in os.getenv('JWT_SECRET_KEY', ''):
            print("توليد مفاتيح أمان...")
            
            # قراءة ملف .env
            with open('.env', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # توليد مفاتيح جديدة
            jwt_secret = secrets.token_urlsafe(64)
            csrf_secret = secrets.token_urlsafe(64)
            master_key = secrets.token_urlsafe(64)
            flask_secret = secrets.token_urlsafe(64)
            
            # استبدال القيم
            content = content.replace('your-jwt-secret-key-change-this-in-production', jwt_secret)
            content = content.replace('your-csrf-secret-key-change-this-in-production', csrf_secret)
            content = content.replace('your-master-encryption-key-change-this-in-production', master_key)
            content = content.replace('qr-viewer-secret-key-2024-firebase-enhanced', flask_secret)
            
            # حفظ الملف
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(content)
            
            # إعادة تحميل
            load_dotenv()
            print("تم توليد المفاتيح بنجاح")
        
        print("تحميل التطبيق...")
        
        # تعطيل الحماية المفرطة مؤقتاً
        os.environ['DISABLE_EXCESSIVE_SECURITY'] = 'true'
        
        # استيراد التطبيق الأصلي
        from app import app, init_firebase
        
        print("تهيئة Firebase...")
        try:
            init_firebase()
            print("تم تهيئة Firebase بنجاح")
        except Exception as e:
            print(f"تحذير Firebase: {e}")
            print("سيتم استخدام الملف المحلي")
        
        # إعداد السيرفر
        port = int(os.environ.get('PORT', 21226))
        
        print(f"\nالسيرفر جاهز:")
        print(f"الرابط: http://localhost:{port}")
        print(f"فحص الصحة: http://localhost:{port}/health")
        print("\nاضغط Ctrl+C لإيقاف السيرفر")
        print("-" * 40)
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\nتم إيقاف السيرفر")
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
