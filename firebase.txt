******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyDMV3d2vzPwxlMbyRRfZUiyXG2t8TYXf8c",
  authDomain: "car-report-a01fe.firebaseapp.com",
  databaseURL: "https://car-report-a01fe-default-rtdb.firebaseio.com",
  projectId: "car-report-a01fe",
  storageBucket: "car-report-a01fe.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:8c74579cbc72e05e334cc0",
  measurementId: "G-20V5S74DD8"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);