{"rules": {".read": false, ".write": false, "contracts": {".indexOn": ["uuid", "serial_number", "created_at"], "$contractId": {".read": "auth != null && (auth.uid == 'admin' || auth.token.permissions.hasAny(['read', 'admin']))", ".write": "auth != null && (auth.uid == 'admin' || auth.token.permissions.hasAny(['write', 'admin']))", ".validate": "newData.hasChildren(['uuid', 'serial_number', 'date']) && newData.child('uuid').isString() && newData.child('serial_number').isString()", "uuid": {".validate": "newData.isString() && newData.val().matches(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)"}, "serial_number": {".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50"}, "date": {".validate": "newData.isString() && newData.val().length > 0"}, "seller_name": {".validate": "newData.isString() && newData.val().length <= 100"}, "buyer_name": {".validate": "newData.isString() && newData.val().length <= 100"}, "car_make": {".validate": "newData.isString() && newData.val().length <= 50"}, "sale_amount": {".validate": "newData.isString() || newData.isNumber()"}, "created_at": {".validate": "newData.isString()"}, "computer_name": {".validate": "newData.isString() && newData.val().length <= 100"}}}, "admin_config": {".read": "auth != null && auth.uid == 'admin'", ".write": "auth != null && auth.uid == 'admin'", ".validate": "newData.hasChild('password')", "password": {".validate": "newData.isString() && newData.val().length >= 8"}}, "serial_number": {".read": "auth != null && (auth.uid == 'admin' || auth.token.permissions.hasAny(['read', 'admin']))", ".write": "auth != null && (auth.uid == 'admin' || auth.token.permissions.hasAny(['write', 'admin']))", ".validate": "newData.isNumber() && newData.val() >= 0"}, "computers": {".read": "auth != null && auth.uid == 'admin'", ".write": "auth != null && auth.uid == 'admin'", "$computerId": {".validate": "newData.hasChildren(['name', 'permissions'])", "name": {".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 100"}, "permissions": {".validate": "newData.hasChildren(['can_edit', 'can_download'])", "can_edit": {".validate": "newData.isBoolean()"}, "can_download": {".validate": "newData.isBoolean()"}}, "last_access": {".validate": "newData.isString()"}}}, "security_logs": {".read": "auth != null && auth.uid == 'admin'", ".write": "auth != null", "$logId": {".validate": "newData.hasChildren(['timestamp', 'event_type', 'ip'])", "timestamp": {".validate": "newData.isString()"}, "event_type": {".validate": "newData.isString() && newData.val().length > 0"}, "ip": {".validate": "newData.isString() && newData.val().length > 0"}, "user_agent": {".validate": "newData.isString()"}, "details": {".validate": "newData.hasChildren()"}}}}}