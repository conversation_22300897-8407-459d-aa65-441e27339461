"""
HTTPS Enforcement and Transport Security
فرض HTTPS وأمان النقل

This module enforces HTTPS connections and provides transport layer security
يفرض هذا الملف اتصالات HTTPS ويوفر أمان طبقة النقل
"""

import os
from flask import Flask, request, redirect, url_for, abort
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class HTTPSEnforcer:
    """
    HTTPS enforcement with advanced security features
    فرض HTTPS مع ميزات أمان متقدمة
    """
    
    def __init__(self, app: Flask = None):
        self.app = app
        self.https_only = os.getenv('HTTPS_ONLY', 'true').lower() == 'true'
        self.hsts_max_age = int(os.getenv('HSTS_MAX_AGE', '31536000'))  # 1 year
        self.hsts_include_subdomains = os.getenv('HSTS_INCLUDE_SUBDOMAINS', 'true').lower() == 'true'
        self.hsts_preload = os.getenv('HSTS_PRELOAD', 'true').lower() == 'true'
        
        if app:
            self.init_app(app)
    
    def init_app(self, app: Flask):
        """Initialize HTTPS enforcement for Flask app"""
        self.app = app
        
        # Add before_request handler
        app.before_request(self.enforce_https)
        
        # Add after_request handler for security headers
        app.after_request(self.add_transport_security_headers)
        
        logger.info("[SECURITY] HTTPS enforcement initialized")
    
    def enforce_https(self):
        """
        Enforce HTTPS connections
        فرض اتصالات HTTPS
        """
        try:
            if not self.https_only:
                return  # HTTPS enforcement disabled
            
            # Skip enforcement for health checks and local development
            if request.endpoint in ['health_check'] or request.remote_addr in ['127.0.0.1', 'localhost']:
                return
            
            # Check if request is secure
            if not request.is_secure:
                # Check for forwarded protocol headers (for reverse proxies)
                forwarded_proto = request.headers.get('X-Forwarded-Proto', '').lower()
                forwarded_ssl = request.headers.get('X-Forwarded-SSL', '').lower()
                
                if forwarded_proto != 'https' and forwarded_ssl != 'on':
                    # Redirect to HTTPS
                    https_url = request.url.replace('http://', 'https://', 1)
                    logger.warning(f"[SECURITY] Redirecting HTTP to HTTPS: {request.url} -> {https_url}")
                    return redirect(https_url, code=301)
                    
        except Exception as e:
            logger.error(f"❌ HTTPS enforcement error: {e}")
    
    def add_transport_security_headers(self, response):
        """
        Add transport security headers
        إضافة headers أمان النقل
        """
        try:
            # HTTP Strict Transport Security (HSTS)
            hsts_value = f'max-age={self.hsts_max_age}'
            
            if self.hsts_include_subdomains:
                hsts_value += '; includeSubDomains'
            
            if self.hsts_preload:
                hsts_value += '; preload'
            
            response.headers['Strict-Transport-Security'] = hsts_value
            
            # Additional transport security headers
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            
            # Content Security Policy
            csp_directives = [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' https://www.gstatic.com https://www.googleapis.com",
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
                "font-src 'self' https://fonts.gstatic.com",
                "img-src 'self' data: https:",
                "connect-src 'self' https://identitytoolkit.googleapis.com https://*.firebaseio.com",
                "frame-ancestors 'none'",
                "base-uri 'self'",
                "form-action 'self'",
                "upgrade-insecure-requests"
            ]
            
            response.headers['Content-Security-Policy'] = '; '.join(csp_directives)
            
            # Permissions Policy (formerly Feature Policy)
            permissions_policy = [
                'geolocation=()',
                'microphone=()',
                'camera=()',
                'payment=()',
                'usb=()',
                'magnetometer=()',
                'gyroscope=()',
                'speaker=()',
                'vibrate=()',
                'fullscreen=(self)',
                'sync-xhr=()'
            ]
            
            response.headers['Permissions-Policy'] = ', '.join(permissions_policy)
            
            # Cache control for security
            if request.endpoint and 'api' in request.endpoint:
                response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Transport security headers error: {e}")
            return response

def require_https(f):
    """
    Decorator to require HTTPS for specific routes
    مُزخرف لطلب HTTPS لمسارات محددة
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Check if request is secure
            if not request.is_secure:
                # Check for forwarded protocol headers
                forwarded_proto = request.headers.get('X-Forwarded-Proto', '').lower()
                forwarded_ssl = request.headers.get('X-Forwarded-SSL', '').lower()
                
                if forwarded_proto != 'https' and forwarded_ssl != 'on':
                    logger.warning(f"🔒 HTTPS required for {request.endpoint}")
                    abort(403)  # Forbidden
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"❌ HTTPS requirement check error: {e}")
            abort(500)
    
    return decorated_function

def check_ssl_configuration():
    """
    Check SSL/TLS configuration
    فحص إعداد SSL/TLS
    """
    try:
        import ssl
        import socket
        
        # Check if SSL context can be created
        context = ssl.create_default_context()
        
        # Check for secure protocols
        secure_protocols = [
            ssl.PROTOCOL_TLS,
            ssl.PROTOCOL_TLSv1_2
        ]
        
        # Check cipher suites
        secure_ciphers = [
            'ECDHE+AESGCM',
            'ECDHE+CHACHA20',
            'DHE+AESGCM',
            'DHE+CHACHA20',
            '!aNULL',
            '!MD5',
            '!DSS'
        ]
        
        config_status = {
            'ssl_available': True,
            'secure_protocols': secure_protocols,
            'recommended_ciphers': ':'.join(secure_ciphers),
            'context_created': True
        }
        
        logger.info("✅ SSL configuration check passed")
        return config_status
        
    except ImportError:
        logger.error("❌ SSL module not available")
        return {'ssl_available': False, 'error': 'SSL module not available'}
    except Exception as e:
        logger.error(f"❌ SSL configuration check failed: {e}")
        return {'ssl_available': False, 'error': str(e)}

def generate_ssl_config():
    """
    Generate recommended SSL configuration
    إنشاء إعداد SSL موصى به
    """
    ssl_config = {
        'ssl_context': 'adhoc',  # For development only
        'ssl_protocols': ['TLSv1.2', 'TLSv1.3'],
        'ssl_ciphers': [
            'ECDHE-RSA-AES256-GCM-SHA384',
            'ECDHE-RSA-AES128-GCM-SHA256',
            'ECDHE-RSA-AES256-SHA384',
            'ECDHE-RSA-AES128-SHA256',
            'ECDHE-RSA-AES256-SHA',
            'ECDHE-RSA-AES128-SHA',
            'DHE-RSA-AES256-GCM-SHA384',
            'DHE-RSA-AES128-GCM-SHA256',
            'DHE-RSA-AES256-SHA256',
            'DHE-RSA-AES128-SHA256',
            'DHE-RSA-AES256-SHA',
            'DHE-RSA-AES128-SHA'
        ],
        'ssl_options': [
            'SSL_OP_NO_SSLv2',
            'SSL_OP_NO_SSLv3',
            'SSL_OP_NO_TLSv1',
            'SSL_OP_NO_TLSv1_1',
            'SSL_OP_CIPHER_SERVER_PREFERENCE',
            'SSL_OP_SINGLE_DH_USE',
            'SSL_OP_SINGLE_ECDH_USE'
        ],
        'hsts_max_age': 31536000,  # 1 year
        'hsts_include_subdomains': True,
        'hsts_preload': True
    }
    
    return ssl_config

# Global HTTPS enforcer instance
https_enforcer = HTTPSEnforcer()

# Convenience functions
def init_https_enforcement(app: Flask):
    """Initialize HTTPS enforcement for Flask app"""
    https_enforcer.init_app(app)

def get_ssl_status():
    """Get SSL configuration status"""
    return check_ssl_configuration()

def get_recommended_ssl_config():
    """Get recommended SSL configuration"""
    return generate_ssl_config()
