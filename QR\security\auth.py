"""
Advanced Authentication and Authorization System
نظام المصادقة والتفويض المتقدم

This module provides JWT-based authentication with CSRF protection
يوفر هذا الملف مصادقة JWT مع حماية CSRF
"""

import os
import jwt
import secrets
import hashlib
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, session, current_app
import logging

logger = logging.getLogger(__name__)

class SecureAuth:
    """
    Advanced authentication system with multiple security layers
    نظام المصادقة المتقدم مع طبقات أمان متعددة
    """
    
    def __init__(self):
        self.jwt_secret = os.getenv('JWT_SECRET_KEY', secrets.token_urlsafe(64))
        self.csrf_secret = os.getenv('CSRF_SECRET_KEY', secrets.token_urlsafe(64))
        self.token_expiry = int(os.getenv('JWT_EXPIRY_HOURS', 24))
        self.max_login_attempts = int(os.getenv('MAX_LOGIN_ATTEMPTS', 5))
        self.lockout_duration = int(os.getenv('LOCKOUT_DURATION_MINUTES', 30))
        self.failed_attempts = {}  # In production, use Redis or database
        
        if not os.getenv('JWT_SECRET_KEY'):
            logger.warning("⚠️ JWT_SECRET_KEY not set. Generated temporary key.")
            logger.warning(f"JWT_SECRET_KEY={self.jwt_secret}")
    
    def generate_csrf_token(self) -> str:
        """Generate CSRF token for form protection"""
        try:
            token_data = {
                'csrf': secrets.token_urlsafe(32),
                'timestamp': datetime.utcnow().isoformat(),
                'session_id': session.get('session_id', secrets.token_urlsafe(16))
            }
            
            csrf_token = jwt.encode(
                token_data,
                self.csrf_secret,
                algorithm='HS256'
            )
            
            # Store in session for verification
            session['csrf_token'] = csrf_token
            
            return csrf_token
            
        except Exception as e:
            logger.error(f"❌ CSRF token generation failed: {e}")
            raise
    
    def verify_csrf_token(self, token: str) -> bool:
        """Verify CSRF token"""
        try:
            if not token:
                return False
            
            # Check if token matches session
            session_token = session.get('csrf_token')
            if not session_token or token != session_token:
                return False
            
            # Decode and verify token
            payload = jwt.decode(
                token,
                self.csrf_secret,
                algorithms=['HS256']
            )
            
            # Check timestamp (token valid for 1 hour)
            token_time = datetime.fromisoformat(payload['timestamp'])
            if datetime.utcnow() - token_time > timedelta(hours=1):
                return False
            
            return True
            
        except jwt.InvalidTokenError:
            logger.warning("⚠️ Invalid CSRF token")
            return False
        except Exception as e:
            logger.error(f"❌ CSRF verification failed: {e}")
            return False
    
    def generate_jwt_token(self, user_id: str, permissions: list = None) -> str:
        """Generate JWT token for authenticated user"""
        try:
            payload = {
                'user_id': user_id,
                'permissions': permissions or [],
                'iat': datetime.utcnow(),
                'exp': datetime.utcnow() + timedelta(hours=self.token_expiry),
                'jti': secrets.token_urlsafe(16),  # JWT ID for token revocation
                'session_id': session.get('session_id', secrets.token_urlsafe(16))
            }
            
            token = jwt.encode(
                payload,
                self.jwt_secret,
                algorithm='HS256'
            )
            
            # Store token info in session
            session['jwt_token'] = token
            session['user_id'] = user_id
            session['permissions'] = permissions or []
            
            logger.info(f"🔑 JWT token generated for user: {user_id}")
            return token
            
        except Exception as e:
            logger.error(f"❌ JWT token generation failed: {e}")
            raise
    
    def verify_jwt_token(self, token: str) -> dict:
        """Verify and decode JWT token"""
        try:
            if not token:
                raise jwt.InvalidTokenError("No token provided")
            
            payload = jwt.decode(
                token,
                self.jwt_secret,
                algorithms=['HS256']
            )
            
            # Additional security checks
            session_token = session.get('jwt_token')
            if session_token != token:
                raise jwt.InvalidTokenError("Token doesn't match session")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("⚠️ JWT token expired")
            raise
        except jwt.InvalidTokenError as e:
            logger.warning(f"⚠️ Invalid JWT token: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ JWT verification failed: {e}")
            raise
    
    def check_rate_limit(self, identifier: str) -> bool:
        """Check if user/IP is rate limited"""
        try:
            current_time = datetime.utcnow()
            
            if identifier in self.failed_attempts:
                attempts_data = self.failed_attempts[identifier]
                
                # Check if lockout period has expired
                if current_time - attempts_data['last_attempt'] > timedelta(minutes=self.lockout_duration):
                    del self.failed_attempts[identifier]
                    return True
                
                # Check if max attempts exceeded
                if attempts_data['count'] >= self.max_login_attempts:
                    logger.warning(f"🚫 Rate limit exceeded for: {identifier}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Rate limit check failed: {e}")
            return True  # Allow on error to prevent lockout
    
    def record_failed_attempt(self, identifier: str):
        """Record failed login attempt"""
        try:
            current_time = datetime.utcnow()
            
            if identifier in self.failed_attempts:
                self.failed_attempts[identifier]['count'] += 1
                self.failed_attempts[identifier]['last_attempt'] = current_time
            else:
                self.failed_attempts[identifier] = {
                    'count': 1,
                    'last_attempt': current_time
                }
            
            logger.warning(f"⚠️ Failed login attempt for: {identifier}")
            
        except Exception as e:
            logger.error(f"❌ Failed to record attempt: {e}")
    
    def clear_failed_attempts(self, identifier: str):
        """Clear failed attempts after successful login"""
        try:
            if identifier in self.failed_attempts:
                del self.failed_attempts[identifier]
                
        except Exception as e:
            logger.error(f"❌ Failed to clear attempts: {e}")
    
    def get_client_identifier(self) -> str:
        """Get unique client identifier for rate limiting"""
        try:
            # Use IP + User-Agent for identification
            ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            user_agent = request.headers.get('User-Agent', '')
            
            # Create hash of IP + User-Agent
            identifier = hashlib.sha256(f"{ip}:{user_agent}".encode()).hexdigest()[:16]
            
            return identifier
            
        except Exception as e:
            logger.error(f"❌ Failed to get client identifier: {e}")
            return 'unknown'

# Global auth instance
auth_manager = SecureAuth()

# Decorators for route protection
def require_auth(f):
    """Decorator to require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Get token from header or session
            token = request.headers.get('Authorization')
            if token and token.startswith('Bearer '):
                token = token[7:]
            else:
                token = session.get('jwt_token')
            
            if not token:
                return jsonify({'error': 'Authentication required'}), 401
            
            # Verify token
            payload = auth_manager.verify_jwt_token(token)
            request.user = payload
            
            return f(*args, **kwargs)
            
        except jwt.ExpiredSignatureError:
            return jsonify({'error': 'Token expired'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': 'Invalid token'}), 401
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return jsonify({'error': 'Authentication failed'}), 401
    
    return decorated_function

def require_csrf(f):
    """Decorator to require CSRF token"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Skip CSRF for GET requests
            if request.method == 'GET':
                return f(*args, **kwargs)
            
            # Get CSRF token from header or form
            csrf_token = request.headers.get('X-CSRF-Token') or request.form.get('csrf_token')
            
            if not csrf_token:
                return jsonify({'error': 'CSRF token required'}), 403
            
            # Verify CSRF token
            if not auth_manager.verify_csrf_token(csrf_token):
                return jsonify({'error': 'Invalid CSRF token'}), 403
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"❌ CSRF verification error: {e}")
            return jsonify({'error': 'CSRF verification failed'}), 403
    
    return decorated_function

def require_permission(permission: str):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                user = getattr(request, 'user', None)
                if not user:
                    return jsonify({'error': 'Authentication required'}), 401
                
                user_permissions = user.get('permissions', [])
                if permission not in user_permissions and 'admin' not in user_permissions:
                    return jsonify({'error': 'Insufficient permissions'}), 403
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"❌ Permission check error: {e}")
                return jsonify({'error': 'Permission check failed'}), 403
        
        return decorated_function
    return decorator
