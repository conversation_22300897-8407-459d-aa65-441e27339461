# Flask Framework and Web Server
Flask==2.3.3
gunicorn==21.2.0
Werkzeug==2.3.7

# Document Processing
python-docx==0.8.11
docx2pdf==0.1.8

# PDF Processing
PyPDF2==3.0.1
reportlab==4.4.2

# Excel Processing
openpyxl==3.1.2

# Image Processing
Pillow==11.2.1

# QR Code Generation
qrcode[pil]==7.4.2

# Gradio Client for Word to PDF conversion
gradio_client==0.8.1

# Firebase Integration
firebase-admin==6.2.0

# Environment Variables
python-dotenv==1.0.0

# Windows COM Support (for docx2pdf on Windows)
pywin32==310; sys_platform == "win32"

# Note: The following packages are part of Python standard library:
# - uuid (built-in)
# - concurrent.futures (built-in since Python 3.2)
# - asyncio (built-in since Python 3.4)
# - threading, queue, subprocess, platform, sys, os, base64, json, time, locale, datetime, io (all built-in)
