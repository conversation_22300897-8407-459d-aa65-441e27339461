<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الإدارة - مكتب علي العبدلي</title>
    <style>
        body {
            font-family: 'Traditional Arabic', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            direction: rtl;
        }

        .main-content {
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 35px;
            border-radius: 12px;
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid #e8e9ea;
        }
        
        .header {
            text-align: center;
            margin-bottom: 35px;
            padding-bottom: 25px;
            border-bottom: 2px solid #007bff;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 12px;
        }
        
        .header .admin-info {
            background-color: #f0f8ff;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #d0e7ff;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        
        .stat-card .number {
            font-size: 36px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .section {
            margin-bottom: 35px;
            padding: 25px;
            border: 1px solid #e8e9ea;
            border-radius: 12px;
            background-color: #f8f9fa;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 6px;
            color: #2c3e50;
            font-weight: 600;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d9e0;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        input[type="text"]:focus, input[type="password"]:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #545b62, #495057);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
        
        .success {
            background-color: #e6ffe6;
            color: #006600;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #ccffcc;
        }
        
        .error {
            background-color: #ffe6e6;
            color: #d00;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #ffcccc;
        }
        
        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .users-table th, .users-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        
        .users-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .users-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .logout-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: #666;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .logout-btn:hover {
            background-color: #555;
        }
    </style>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    {% include 'toolbar.html' %}

    <div class="main-content">
        <div class="container">
        <div class="header">
            <h1>🔐 لوحة تحكم الإدارة</h1>
            <div class="admin-info">
                <strong>مرحباً بك في لوحة تحكم الإدارة</strong>
            </div>
        </div>
        
        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>الرقم التسلسلي الحالي</h3>
                <div class="number">{{ current_serial }}</div>
            </div>
            <div class="stat-card">
                <h3>عدد المستخدمين</h3>
                <div class="number">{{ computers|length }}</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي العقود</h3>
                <div class="number">
                    {% set total_contracts = computers.values() | map(attribute='contracts_count', default=0) | sum %}
                    {{ total_contracts }}
                </div>
            </div>
        </div>
        
        <!-- الرسائل -->
        {% if success %}
        <div class="success">{{ success }}</div>
        {% endif %}
        
        {% if error %}
        <div class="error">{{ error }}</div>
        {% endif %}
        
        <!-- إنشاء مستخدم جديد -->
        <div class="section">
            <h2>إنشاء مستخدم جديد</h2>
            <form method="POST" action="{{ url_for('admin') }}">
                <input type="hidden" name="action" value="create_user">
                <div class="form-group">
                    <label for="computer_name">اسم الحاسوب:</label>
                    <input type="text" id="computer_name" name="computer_name" required
                           placeholder="أدخل اسم فريد للحاسوب" maxlength="50">
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" required
                           placeholder="أدخل كلمة المرور" maxlength="50">
                </div>
                <button type="submit" class="btn btn-primary">إنشاء المستخدم</button>
            </form>
        </div>
        
        <!-- تغيير كلمة مرور الإدارة -->
        <div class="section">
            <h2>تغيير كلمة مرور الإدارة</h2>
            <form method="POST" action="{{ url_for('admin') }}">
                <input type="hidden" name="action" value="change_password">
                <div class="form-group">
                    <label for="new_admin_password">كلمة المرور الجديدة:</label>
                    <input type="password" id="new_admin_password" name="new_admin_password" required
                           placeholder="أدخل كلمة مرور الإدارة الجديدة" maxlength="50">
                </div>
                <button type="submit" class="btn btn-secondary">تغيير كلمة المرور</button>
            </form>
        </div>
        
        <!-- إدارة العقود -->
        <div class="section">
            <h2>إدارة العقود</h2>
            <div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
                <a href="/contracts" class="btn btn-primary">📋 إدارة العقود المبرمة</a>
                <a href="/admin/annual_export" class="btn btn-primary">📊 تحميل عقود السنة</a>
                <button class="btn btn-secondary" onclick="loadContracts()">📝 عرض المسودات المحفوظة</button>
            </div>

            <div id="contracts-list" style="margin-top: 20px;">
                <p>اضغط على "عرض المسودات المحفوظة" لعرض العقود المحفوظة كمسودات</p>
            </div>
        </div>

        <!-- إدارة الصلاحيات -->
        <div class="section">
            <h2>إدارة صلاحيات المستخدمين</h2>
            <div id="permissions-management">
                {% for name, data in computers.items() %}
                <div style="border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px;">
                    <h4>{{ name }} (ID: {{ data.id }})</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 10px;">
                        <label>
                            <input type="checkbox" id="create_{{ name }}"
                                   {% if data.get('permissions', {}).get('create_contracts', true) %}checked{% endif %}>
                            إنشاء عقود جديدة
                        </label>
                        <label>
                            <input type="checkbox" id="edit_{{ name }}"
                                   {% if data.get('permissions', {}).get('edit_saved_contracts', true) %}checked{% endif %}>
                            تعديل العقود المحفوظة
                        </label>
                        <label>
                            <input type="checkbox" id="view_{{ name }}"
                                   {% if data.get('permissions', {}).get('view_serial_number', true) %}checked{% endif %}>
                            عرض الرقم التسلسلي
                        </label>
                        <label>
                            <input type="checkbox" id="edit_finalized_{{ name }}"
                                   {% if data.get('permissions', {}).get('edit_finalized_contracts', false) %}checked{% endif %}>
                            تعديل العقود المبرمة
                        </label>
                        <label>
                            <input type="checkbox" id="download_finalized_{{ name }}"
                                   {% if data.get('permissions', {}).get('download_finalized_contracts', false) %}checked{% endif %}>
                            تحميل العقود المبرمة
                        </label>
                    </div>
                </div>
                {% endfor %}

                <!-- زر تحديث الصلاحيات للجميع -->
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary" style="font-size: 16px; padding: 12px 30px;" onclick="updateAllPermissions()">
                        💾 تحديث صلاحيات جميع المستخدمين
                    </button>
                </div>
            </div>
        </div>

        <!-- قائمة المستخدمين -->
        <div class="section">
            <h2>قائمة المستخدمين</h2>
            <a href="{{ url_for('computers_list') }}" class="btn btn-primary">عرض قائمة مفصلة</a>

            <table class="users-table">
                <thead>
                    <tr>
                        <th>رقم الحاسوب</th>
                        <th>اسم الحاسوب</th>
                        <th>تاريخ التسجيل</th>
                        <th>عدد العقود</th>
                        <th>عقد محفوظ</th>
                    </tr>
                </thead>
                <tbody>
                    {% for name, data in computers.items() %}
                    <tr>
                        <td>{{ data.id }}</td>
                        <td>{{ name }}</td>
                        <td>{{ data.created_at }}</td>
                        <td>{{ data.get('contracts_count', 0) }}</td>
                        <td>{{ '✓' if data.get('has_saved_contract', false) else '✗' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // تحميل قائمة العقود المحفوظة
        function loadContracts() {
            fetch('/admin/api/contracts')
                .then(response => response.json())
                .then(data => {
                    const contractsList = document.getElementById('contracts-list');

                    if (data.success && data.contracts.length > 0) {
                        let html = '<table class="users-table"><thead><tr>';
                        html += '<th>اسم الحاسوب</th><th>رقم العقد</th><th>تاريخ الإنشاء</th><th>آخر تعديل</th>';
                        html += '</tr></thead><tbody>';

                        data.contracts.forEach(contract => {
                            html += `<tr>
                                <td>${contract.computer_name}</td>
                                <td>${contract.contract_info.serial_number}</td>
                                <td>${contract.contract_info.created_at}</td>
                                <td>${contract.contract_info.last_modified}</td>
                            </tr>`;
                        });

                        html += '</tbody></table>';
                        contractsList.innerHTML = html;
                    } else {
                        contractsList.innerHTML = '<p>لا توجد عقود محفوظة حالياً</p>';
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل العقود:', error);
                    document.getElementById('contracts-list').innerHTML = '<p style="color: red;">حدث خطأ في تحميل العقود</p>';
                });
        }

        // تحديث صلاحيات المستخدم
        function updatePermissions(computerName) {
            const permissions = {
                create_contracts: document.getElementById(`create_${computerName}`).checked,
                edit_saved_contracts: document.getElementById(`edit_${computerName}`).checked,
                view_serial_number: document.getElementById(`view_${computerName}`).checked,
                edit_finalized_contracts: document.getElementById(`edit_finalized_${computerName}`).checked,
                download_finalized_contracts: document.getElementById(`download_finalized_${computerName}`).checked
            };

            fetch('/admin/api/permissions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    computer_name: computerName,
                    permissions: permissions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم تحديث الصلاحيات بنجاح');
                } else {
                    alert('خطأ في تحديث الصلاحيات: ' + data.message);
                }
            })
            .catch(error => {
                console.error('خطأ في تحديث الصلاحيات:', error);
                alert('حدث خطأ في تحديث الصلاحيات');
            });
        }

        // تحديث صلاحيات جميع المستخدمين
        function updateAllPermissions() {
            const computers = {{ computers|tojson }};
            const allPermissions = {};

            // جمع صلاحيات جميع المستخدمين
            Object.keys(computers).forEach(computerName => {
                allPermissions[computerName] = {
                    create_contracts: document.getElementById(`create_${computerName}`).checked,
                    edit_saved_contracts: document.getElementById(`edit_${computerName}`).checked,
                    view_serial_number: document.getElementById(`view_${computerName}`).checked,
                    edit_finalized_contracts: document.getElementById(`edit_finalized_${computerName}`).checked,
                    download_finalized_contracts: document.getElementById(`download_finalized_${computerName}`).checked
                };
            });

            fetch('/admin/api/permissions/bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    permissions: allPermissions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم تحديث صلاحيات جميع المستخدمين بنجاح');
                } else {
                    alert('خطأ في تحديث الصلاحيات: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('خطأ في تحديث الصلاحيات');
            });
        }
    </script>
        </div>
    </div>
</body>
</html>
