/**
 * وحدة تحويل الأرقام إلى نص عربي فصيح - JavaScript
 * Arabic Number to Text Converter Module - JavaScript Version
 * 
 * هذه الوحدة تقوم بتحويل الأرقام إلى نص باللغة العربية الفصحى
 * مع مراعاة قواعد النحو العربي الصحيحة للعدد والمعدود
 * 
 * المؤلف: نظام إدارة عقود السيارات
 * التاريخ: 2024
 */

class ArabicNumberConverter {
    constructor() {
        // الآحاد (مذكر)
        this.onesMasculine = [
            "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", 
            "ستة", "سبعة", "ثمانية", "تسعة"
        ];
        
        // الآحاد (مؤنث) - للاستخدام مع المعدود المذكر
        this.onesFeminine = [
            "", "واحدة", "اثنتان", "ثلاث", "أربع", "خمس",
            "ست", "سبع", "ثمان", "تسع"
        ];
        
        // العشرات
        this.tens = [
            "", "", "عشرون", "ثلاثون", "أربعون", "خمسون",
            "ستون", "سبعون", "ثمانون", "تسعون"
        ];
        
        // المئات (مع التصحيح النحوي)
        this.hundreds = [
            "", "مئة", "مئتان", "ثلاثمئة", "أربعمئة", "خمسمئة",
            "ستمئة", "سبعمئة", "ثمانمئة", "تسعمئة"
        ];
        
        // الأرقام من 11-19
        this.teens = [
            "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", 
            "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"
        ];
        
        // أسماء العملات مع التصحيح النحوي الكامل
        this.currencies = {
        'USD': {
            'singular': 'دولار أمريكي',               // واحد دولار أمريكي
            'dual': 'دولاران أمريكيان',              // اثنان دولاران أمريكيان
            'plural_few': 'دولارات أمريكية',         // من 3 إلى 10
            'plural_many': 'دولارًا أمريكيًا',       // من 11 فما فوق
            'tamyeez': 'دولارًا أمريكيًا',           // تمييز منصوب
            'symbol': '$',
            'masculine': true,
            'subunit': 'سنتًا',                       // مفرد منصوب
            'subunit_plural': 'سنتات'                 // جمع
        },
        'IQD': {
            'singular': 'دينار عراقي',               // واحد دينار عراقي
            'dual': 'ديناران عراقيان',               // اثنان ديناران عراقيان
            'plural_few': 'دنانير عراقية',           // من 3 إلى 10
            'plural_many': 'دينارًا عراقيًا',         // من 11 فما فوق
            'tamyeez': 'دينارًا عراقيًا',             // تمييز منصوب
            'symbol': '',
            'masculine': true,
            'subunit': 'فلسًا',                        // مفرد منصوب (اختياري حسب النظام)
            'subunit_plural': 'فلوس'                  // جمع (اختياري حسب النظام)
        }
    };
    }
    
    /**
     * تحويل مجموعة من ثلاثة أرقام إلى نص عربي
     */
    _convertGroup(num, isCurrencyMasculine = true) {
        if (num === 0) return "";
        
        let result = "";
        
        // اختيار قائمة الآحاد المناسبة حسب جنس المعدود
        const ones = isCurrencyMasculine ? this.onesMasculine : this.onesFeminine;
        
        // المئات
        if (num >= 100) {
            const hundredsDigit = Math.floor(num / 100);
            result += this.hundreds[hundredsDigit];
            num %= 100;
            if (num > 0) {
                result += " و";
            }
        }
        
        // العشرات والآحاد
        if (num >= 20) {
            const tensDigit = Math.floor(num / 10);
            const onesDigit = num % 10;
            if (onesDigit > 0) {
                // ترتيب صحيح: الآحاد ثم العشرات (مثل: ثمانية وثمانون)
                result += ones[onesDigit] + " و" + this.tens[tensDigit];
            } else {
                result += this.tens[tensDigit];
            }
        } else if (num >= 10) {
            result += this.teens[num - 10];
        } else if (num > 0) {
            result += ones[num];
        }
        
        return result;
    }
    
    /**
     * تحديد الصيغة الصحيحة لاسم العملة حسب العدد مع دعم التمييز المنصوب
     */
    _getCurrencyForm(amount, currencyCode, useTamyeez = false) {
        if (!(currencyCode in this.currencies)) {
            return currencyCode;
        }

        const currency = this.currencies[currencyCode];

        // استخدام التمييز المنصوب للأرقام الكبيرة (100+)
        if (useTamyeez && amount >= 100) {
            return currency.tamyeez;
        }

        // قواعد العدد والمعدود في العربية الفصحى
        if (amount === 1) {
            return currency.singular;
        } else if (amount === 2) {
            return currency.dual;
        } else if (amount >= 3 && amount <= 10) {
            return currency.plural_few;
        } else if (amount >= 11 && amount <= 99) { // الأرقام من 11 إلى 99
            return currency.plural_many;
        } else if (amount % 100 === 0) { // الأرقام المئوية (100، 200، 1000، إلخ)
            return currency.singular;
        } else if (amount % 100 === 1) { // الأرقام التي تنتهي بـ 1 (مثل 101، 1001)
            return currency.singular;
        } else if (amount % 100 === 2) { // الأرقام التي تنتهي بـ 2 (مثل 102، 1002)
            return currency.dual;
        } else if (amount % 100 >= 3 && amount % 100 <= 10) { // الأرقام التي تنتهي بـ 3-10
            return currency.plural_few;
        } else if (amount % 100 >= 11 && amount % 100 <= 99) { // الأرقام التي تنتهي بـ 11-99
            return currency.plural_many;
        } else { // باقي الأرقام
            return currency.singular;
        }
    }

    /**
     * تحديد متى نستخدم التمييز المنصوب وفق القواعد النحوية الصحيحة
     */
    _shouldUseTamyeez(number) {
        // التمييز المنصوب يُستخدم في الحالات التالية:
        // 1. الأرقام المركبة التي تحتوي على أجزاء من 11+ في الجزء الأخير
        // 2. الأرقام الكبيرة المركبة

        if (number < 11) {
            return false;
        }

        // المئات المفردة (100، 200، 300، إلخ) لا تحتاج تمييز منصوب
        if (number % 100 === 0 && number < 1000) {
            return false;
        }

        // الآلاف المفردة (1000، 2000، 3000، إلخ) لا تحتاج تمييز منصوب
        if (number % 1000 === 0) {
            return false;
        }

        // الملايين المفردة لا تحتاج تمييز منصوب
        if (number % 1000000 === 0) {
            return false;
        }

        // الأرقام من 11-99 تحتاج تمييز منصوب
        if (number >= 11 && number <= 99) {
            return true;
        }

        // الأرقام المركبة التي تنتهي بـ 11-99 تحتاج تمييز منصوب
        const lastTwoDigits = number % 100;
        if (lastTwoDigits >= 11 && lastTwoDigits <= 99) {
            return true;
        }

        // الأرقام المركبة الأخرى (مثل 101، 102، 103) لا تحتاج تمييز منصوب
        return false;
    }

    /**
     * تحويل الأرقام الكبيرة (آلاف، ملايين، مليارات)
     */
    _convertLargeNumber(number, isCurrencyMasculine = true, hasMillions = false) {
        if (number === 0) return "صفر";

        if (number < 1000) {
            return this._convertGroup(number, isCurrencyMasculine);
        }

        let result = "";
        const originalNumber = number;

        // المليارات
        if (number >= 1000000000) {
            const billions = Math.floor(number / 1000000000);
            if (billions === 1) {
                result += "مليار";
            } else if (billions === 2) {
                result += "ملياران";
            } else if (billions <= 10) {
                result += this._convertGroup(billions, isCurrencyMasculine) + " مليارات";
            } else {
                result += this._convertGroup(billions, isCurrencyMasculine) + " مليار";
            }

            number %= 1000000000;
            if (number > 0) {
                result += " و";
            }
        }

        // الملايين
        if (number >= 1000000) {
            const millions = Math.floor(number / 1000000);
            if (millions === 1) {
                result += "مليون";
            } else if (millions === 2) {
                result += "مليونان";
            } else if (millions <= 10) {
                result += this._convertGroup(millions, isCurrencyMasculine) + " ملايين";
            } else {
                result += this._convertGroup(millions, isCurrencyMasculine) + " مليون";
            }

            number %= 1000000;
            hasMillions = true; // تم العثور على ملايين
            if (number > 0) {
                result += " و";
            }
        }

        // الآلاف (مع التمييز المنصوب الصحيح)
        if (number >= 1000) {
            const thousands = Math.floor(number / 1000);
            if (thousands === 1) {
                result += "ألف";
            } else if (thousands === 2) {
                result += "ألفان";
            } else if (thousands <= 10) {
                result += this._convertGroup(thousands, isCurrencyMasculine) + " آلاف";
            } else {
                // استخدام "ألفًا" فقط إذا لم تكن هناك ملايين
                if (hasMillions || originalNumber >= 1000000) {
                    result += this._convertGroup(thousands, isCurrencyMasculine) + " ألف";
                } else {
                    result += this._convertGroup(thousands, isCurrencyMasculine) + " ألفًا";
                }
            }

            number %= 1000;
            if (number > 0) {
                result += " و";
            }
        }

        // المئات والعشرات والآحاد
        if (number > 0) {
            result += this._convertGroup(number, isCurrencyMasculine);
        }

        return result;
    }
    
    /**
     * تحويل الرقم إلى نص عربي فصيح مع العملة
     */
    convertNumberToArabicText(amount, currency = 'IQD') {
        // التحقق من صحة الإدخال
        if (typeof amount !== 'number' || amount < 0) {
            throw new Error("المبلغ يجب أن يكون رقمًا موجبًا");
        }
        
        if (!(currency in this.currencies)) {
            throw new Error(`العملة غير مدعومة: ${currency}. العملات المدعومة: ${Object.keys(this.currencies).join(', ')}`);
        }
        
        // التعامل مع الصفر
        if (amount === 0) {
            return `صفر ${this.currencies[currency].singular}`;
        }
        
        // فصل الجزء الصحيح والعشري
        const integerPart = Math.floor(amount);
        const decimalPart = Math.round((amount - integerPart) * 100);
        
        // تحويل الجزء الصحيح
        const currencyInfo = this.currencies[currency];
        const isMasculine = currencyInfo.masculine;
        
        let text = this._convertLargeNumber(integerPart, isMasculine);
        
        // إضافة اسم العملة (مع التمييز المنصوب للأرقام المركبة فقط)
        const useTamyeez = this._shouldUseTamyeez(integerPart);
        const currencyName = this._getCurrencyForm(integerPart, currency, useTamyeez);
        text += " " + currencyName;
        
        // إضافة الجزء العشري إذا وُجد
        if (decimalPart > 0) {
            const subunitText = this._convertLargeNumber(decimalPart, isMasculine);
            if (decimalPart === 1) {
                text += " و" + subunitText + " " + currencyInfo.subunit;
            } else {
                text += " و" + subunitText + " " + currencyInfo.subunit_plural;
            }
        }
        
        return text;
    }
    
    /**
     * تنسيق الرقم بالفواصل والرمز النقدي
     */
    formatNumberWithCurrency(amount, currency = 'IQD') {
        if (typeof amount !== 'number' || amount < 0) {
            return "0";
        }
        
        if (amount === 0) {
            const symbol = this.currencies[currency]?.symbol || '';
            return symbol ? `0${symbol}` : "0";
        }
        
        // تنسيق الرقم بالفواصل
        let formatted;
        if (amount !== Math.floor(amount)) {
            formatted = amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        } else {
            formatted = Math.floor(amount).toLocaleString();
        }
        
        // إضافة رمز العملة
        const symbol = this.currencies[currency]?.symbol || '';
        return symbol ? `${formatted}${symbol}` : formatted;
    }
}

// إنشاء مثيل عام للاستخدام
const arabicConverter = new ArabicNumberConverter();

// دوال مساعدة للاستخدام المباشر
function convertNumberToArabicText(amount, currency = 'IQD') {
    try {
        return arabicConverter.convertNumberToArabicText(amount, currency);
    } catch (error) {
        console.error('خطأ في تحويل الرقم:', error);
        return currency === 'USD' ? 'صفر دولار أمريكي' : 'صفر دينار عراقي';
    }
}

function formatNumberWithCurrency(amount, currency = 'IQD') {
    try {
        return arabicConverter.formatNumberWithCurrency(amount, currency);
    } catch (error) {
        console.error('خطأ في تنسيق الرقم:', error);
        return "0";
    }
}
