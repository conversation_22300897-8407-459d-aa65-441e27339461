#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات QR Code للعقود
======================

هذا الملف يحتوي على إعدادات QR Code التي يمكن تخصيصها بسهولة.
"""

# إعدادات الدومين لـ QR Code
# ============================================

# الدومين الأساسي لعرض العقود عبر QR Code
# يجب تغيير هذا إلى الدومين الفعلي لخدمة عرض العقود
QR_VIEWER_DOMAIN = "https://425243.site.bot-hosting.net"

# بدائل للدومين (يمكن استخدامها للتطوير أو البيئات المختلفة)
DOMAINS = {
    'production': 'https://425243.site.bot-hosting.net',
    'staging': 'https://425243.site.bot-hosting.net',
    'development': 'https://425243.site.bot-hosting.net',
    'local': 'https://425243.site.bot-hosting.net'
}

# البيئة الحالية (يمكن تغييرها حسب الحاجة)
CURRENT_ENVIRONMENT = 'production'  # غير هذا إلى 'production' عند النشر

def get_qr_domain():
    """
    الحصول على الدومين المناسب لـ QR Code حسب البيئة الحالية
    
    Returns:
        str: الدومين المناسب
    """
    return DOMAINS.get(CURRENT_ENVIRONMENT, QR_VIEWER_DOMAIN)

def get_contract_qr_url(contract_uuid):
    """
    إنشاء رابط QR Code للعقد
    
    Args:
        contract_uuid (str): UUID الخاص بالعقد
        
    Returns:
        str: الرابط الكامل للعقد
    """
    domain = get_qr_domain()
    return f"{domain}/contract/{contract_uuid}"

# إعدادات QR Code التقنية
# ========================

QR_SETTINGS = {
    'version': 1,  # أصغر حجم ممكن
    'error_correction': 'L',  # أقل مستوى تصحيح خطأ
    'box_size': 2,  # حجم صغير للمربعات
    'border': 1,  # حد أدنى للحدود
    'fill_color': 'black',
    'back_color': 'white'
}

# إعدادات الحجم في Word
WORD_QR_SIZE = {
    'width_inches': 0.7,
    'height_inches': 0.7
}

# رسائل التشخيص
DEBUG_MESSAGES = {
    'qr_generated': "✅ تم إنشاء QR code: {path}",
    'qr_inserted': "✅ تم إدراج QR code في جدول العقد: {path}",
    'qr_inserted_paragraph': "✅ تم إدراج QR code في فقرة العقد: {path}",
    'qr_error': "❌ خطأ في إنشاء QR code: {error}",
    'qr_url': "🔗 رابط QR: {url}"
}

def print_qr_debug(message_key, **kwargs):
    """
    طباعة رسائل التشخيص لـ QR Code
    
    Args:
        message_key (str): مفتاح الرسالة
        **kwargs: متغيرات الرسالة
    """
    if message_key in DEBUG_MESSAGES:
        message = DEBUG_MESSAGES[message_key].format(**kwargs)
        print(message)

# تعليمات الإعداد
SETUP_INSTRUCTIONS = """
تعليمات إعداد QR Code:
====================

1. تحديد الدومين:
   - غير قيمة QR_VIEWER_DOMAIN إلى الدومين الفعلي لخدمة عرض العقود
   - أو استخدم DOMAINS واختر البيئة المناسبة في CURRENT_ENVIRONMENT

2. إعداد خدمة عرض العقود:
   - تأكد من وجود خدمة منفصلة لعرض العقود على الدومين المحدد
   - الخدمة يجب أن تستقبل طلبات على المسار: /contract/{uuid}

3. اختبار QR Code:
   - بعد إنشاء عقد، امسح QR Code للتأكد من عمله
   - تأكد من أن الرابط يوجه إلى صفحة العقد الصحيحة

4. الأمان:
   - تأكد من أن خدمة عرض العقود آمنة ومحمية
   - استخدم HTTPS في البيئة الإنتاجية

مثال للاستخدام:
================

from qr_config import get_contract_qr_url, QR_SETTINGS

# إنشاء رابط QR للعقد
contract_uuid = "12345678-1234-1234-1234-123456789abc"
qr_url = get_contract_qr_url(contract_uuid)
print(f"رابط QR: {qr_url}")

# الحصول على إعدادات QR
settings = QR_SETTINGS
print(f"إعدادات QR: {settings}")
"""

if __name__ == "__main__":
    print(SETUP_INSTRUCTIONS)
    print(f"\nالدومين الحالي: {get_qr_domain()}")
    print(f"البيئة الحالية: {CURRENT_ENVIRONMENT}")
    
    # مثال على إنشاء رابط
    test_uuid = "12345678-1234-1234-1234-123456789abc"
    test_url = get_contract_qr_url(test_uuid)
    print(f"مثال على رابط QR: {test_url}")
