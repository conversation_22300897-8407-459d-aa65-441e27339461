<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل العقد - مكتب علي العبدلي</title>
    <style>
        body {
            font-family: 'Traditional Arabic', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            direction: rtl;
        }

        .main-content {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: calc(100vh - 70px);
            padding: 20px;
        }
        .container {
            background: white;
            padding: 30px 40px;
            border: 1px solid #e0e6ed;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            max-width: 850px;
            width: 100%;
            text-align: right;
        }

        .header-section {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .header-section h1 {
            margin: 0;
            font-size: 24px;
        }

        .contract-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .form-section {
            margin-bottom: 20px;
            padding: 0 15px;
        }

        .form-row {
            display: flex;
            flex-direction: row-reverse;
            gap: 25px;
            margin-bottom: 12px;
            align-items: flex-start;
        }

        .form-group {
            flex: 1;
            direction: rtl;
        }

        label {
            text-align: right;
            display: block;
            direction: rtl;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 6px;
        }

        .divider {
            border-bottom: 1px solid #e8e9ea;
            margin: 25px -15px;
            width: calc(100% + 30px);
        }

        select {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: left 8px center;
            background-size: 12px;
            padding-left: 24px;
        }

        h2 {
            font-size: 18px;
            margin: 15px 0 10px;
            color: #2c3e50;
        }

        input, select {
            padding: 8px 14px;
            border: 1px solid #d1d9e0;
            border-radius: 6px;
            font-size: 14px;
            direction: rtl;
            text-align: right;
            height: 38px;
            width: 100%;
            box-sizing: border-box;
            background-color: #fff;
            transition: all 0.3s ease;
        }

        input:focus, select:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .btn {
            padding: 10px 18px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #1e7e34;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #545b62;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .cameras-section {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
        }

        .camera-group {
            flex: 1;
            max-width: 45%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .camera-container {
            width: 100%;
            height: 350px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
            margin-bottom: 10px;
        }

        .photo-preview {
            width: 100%;
            height: 100%;
            position: relative;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .photo-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .amount-display {
            margin-top: 5px;
            padding: 8px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 13px;
            color: #495057;
            direction: rtl;
            text-align: right;
            min-height: 20px;
        }

        .amount-display .formatted-number {
            font-weight: bold;
            color: #007bff;
            font-size: 14px;
        }

        .amount-display .arabic-text {
            margin-top: 3px;
            font-style: italic;
            color: #666;
        }

        .action-buttons {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            border-top: 2px solid #dee2e6;
        }

        .form-heading {
            font-size: 16px;
            color: #007bff;
            margin: 10px 0;
            text-align: right;
            padding: 0 15px;
        }

        .alert {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        /* أنماط الكاميرا المحدثة */
        .camera-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: #000;
        }

        .camera-buttons {
            text-align: center;
            margin-top: 15px;
        }

        .capture-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .capture-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .capture-btn:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    {% include 'toolbar.html' %}

    <div class="main-content">
        <div class="container">
        <div class="header-section">
            <h1>تعديل العقد رقم {{ contract_data.get('serial_number', 'غير محدد') }}</h1>
            <p>تم إبرام العقد في: {{ contract_data.get('date', 'غير محدد') }}</p>
        </div>

        <!-- معلومات الحاسوب -->
        <div style="background-color: #f0f8ff; padding: 10px; border-radius: 5px; margin-bottom: 20px; text-align: center; border: 1px solid #d0e7ff;">
            <strong style="color: #007bff;">المستخدم الحالي:</strong>
            <span style="color: #333;">{{ computer_name }}</span>
            <strong style="color: #007bff;">(رقم: {{ computer_id }})</strong>
            <a href="/contracts" style="margin-right: 20px; color: #666; text-decoration: none; font-size: 12px;">العودة للعقود</a>
        </div>

        <div class="alert alert-warning">
            <strong>تنبيه:</strong> أنت تقوم بتعديل عقد مبرم. سيتم حفظ التغييرات وإنشاء نسخة محدثة من العقد.
        </div>

        <form id="editContractForm" method="POST" action="/api/update_contract/{{ contract_id }}">
            <!-- معلومات العقد الأساسية -->
            <div class="contract-info">
                <h3 style="margin: 0 0 15px 0; color: #007bff;">معلومات العقد</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="serial_number">الرقم التسلسلي:</label>
                        <input type="number" id="serial_number" name="serial_number" value="{{ contract_data.get('serial_number', '') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="date">تاريخ العقد:</label>
                        <input type="date" id="date" name="date" value="{{ contract_data.get('date', '') }}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="t">الوقت:</label>
                        <input type="text" id="t" name="t" value="{{ contract_data.get('t', '') }}" placeholder="مثال: 10:30">
                    </div>
                    <div class="form-group">
                        <label for="t_1">الفترة:</label>
                        <select id="t_1" name="t_1" required>
                            <option value="صباحا" {% if contract_data.get('t_1') == 'صباحا' %}selected{% endif %}>صباحا</option>
                            <option value="مساءا" {% if contract_data.get('t_1') == 'مساءا' %}selected{% endif %}>مساءا</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="day">اليوم:</label>
                        <select id="day" name="day" required>
                            <option value="السبت" {% if contract_data.get('day') == 'السبت' %}selected{% endif %}>السبت</option>
                            <option value="الاحد" {% if contract_data.get('day') == 'الاحد' %}selected{% endif %}>الاحد</option>
                            <option value="الاثنين" {% if contract_data.get('day') == 'الاثنين' %}selected{% endif %}>الاثنين</option>
                            <option value="الثلاثاء" {% if contract_data.get('day') == 'الثلاثاء' %}selected{% endif %}>الثلاثاء</option>
                            <option value="الاربعاء" {% if contract_data.get('day') == 'الاربعاء' %}selected{% endif %}>الاربعاء</option>
                            <option value="الخميس" {% if contract_data.get('day') == 'الخميس' %}selected{% endif %}>الخميس</option>
                            <option value="الجمعة" {% if contract_data.get('day') == 'الجمعة' %}selected{% endif %}>الجمعة</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- معلومات المالك الشرعي -->
            <h2 class="form-heading">معلومات المالك الشرعي</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name_1">اسم المالك الشرعي:</label>
                        <input type="text" id="name_1" name="name_1" value="{{ contract_data.get('name_1', '') }}">
                    </div>
                    <div class="form-group">
                        <label for="location_1">عنوان المالك الشرعي:</label>
                        <input type="text" id="location_1" name="location_1" value="{{ contract_data.get('location_1', '') }}">
                    </div>
                </div>
            </div>
            
            <div class="divider"></div>

            <!-- معلومات البائع -->
            <h2 class="form-heading">معلومات البائع</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name_2">اسم البائع:</label>
                        <input type="text" id="name_2" name="name_2" value="{{ contract_data.get('name_2', '') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="location_2">عنوان البائع:</label>
                        <input type="text" id="location_2" name="location_2" value="{{ contract_data.get('location_2', '') }}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="id_1">رقم الهوية:</label>
                        <input type="text" id="id_1" name="id_1" value="{{ contract_data.get('id_1', '') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="phone_1">رقم الهاتف:</label>
                        <input type="text" id="phone_1" name="phone_1" value="{{ contract_data.get('phone_1', '') }}" required>
                    </div>
                </div>
            </div>

            <div class="divider"></div>

            <!-- معلومات المشتري -->
            <h2 class="form-heading">معلومات المشتري</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name_3">اسم المشتري:</label>
                        <input type="text" id="name_3" name="name_3" value="{{ contract_data.get('name_3', '') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="location_3">عنوان المشتري:</label>
                        <input type="text" id="location_3" name="location_3" value="{{ contract_data.get('location_3', '') }}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="id_2">رقم الهوية:</label>
                        <input type="text" id="id_2" name="id_2" value="{{ contract_data.get('id_2', '') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="phone_2">رقم الهاتف:</label>
                        <input type="text" id="phone_2" name="phone_2" value="{{ contract_data.get('phone_2', '') }}" required>
                    </div>
                </div>
            </div>

            <div class="divider"></div>

            <!-- معلومات السيارة -->
            <h2 class="form-heading">معلومات السيارة</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="car_type">نوع السيارة:</label>
                        <input type="text" id="car_type" name="car_type" value="{{ contract_data.get('car_type', '') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="car_model">موديل السيارة:</label>
                        <input type="text" id="car_model" name="car_model" value="{{ contract_data.get('car_model', '') }}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="car_num">رقم السيارة:</label>
                        <input type="text" id="car_num" name="car_num" value="{{ contract_data.get('car_num', '') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="car_colar">لون السيارة:</label>
                        <input type="text" id="car_colar" name="car_colar" value="{{ contract_data.get('car_colar', '') }}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="sasi_num">رقم الشاصي:</label>
                        <input type="text" id="sasi_num" name="sasi_num" value="{{ contract_data.get('sasi_num', '') }}" required>
                    </div>
                    <div class="form-group">
                        <!-- حقل فارغ لتوازن التخطيط -->
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="car_prop">خصائص السيارة:</label>
                        <input type="text" id="car_prop" name="car_prop" value="{{ contract_data.get('car_prop', '') }}">
                    </div>
                    <div class="form-group">
                        <label for="car_city">مدينة التسجيل:</label>
                        <select id="car_city" name="car_city" required>
                            <option value="بغداد" {% if contract_data.get('car_city') == 'بغداد' %}selected{% endif %}>بغداد</option>
                            <option value="نينوى" {% if contract_data.get('car_city') == 'نينوى' %}selected{% endif %}>نينوى</option>
                            <option value="ميسان" {% if contract_data.get('car_city') == 'ميسان' %}selected{% endif %}>ميسان</option>
                            <option value="البصرة" {% if contract_data.get('car_city') == 'البصرة' %}selected{% endif %}>البصرة</option>
                            <option value="الأنبار" {% if contract_data.get('car_city') == 'الأنبار' %}selected{% endif %}>الأنبار</option>
                            <option value="القادسية" {% if contract_data.get('car_city') == 'القادسية' %}selected{% endif %}>القادسية</option>
                            <option value="المثنى" {% if contract_data.get('car_city') == 'المثنى' %}selected{% endif %}>المثنى</option>
                            <option value="ذي قار" {% if contract_data.get('car_city') == 'ذي قار' %}selected{% endif %}>ذي قار</option>
                            <option value="واسط" {% if contract_data.get('car_city') == 'واسط' %}selected{% endif %}>واسط</option>
                            <option value="بابل" {% if contract_data.get('car_city') == 'بابل' %}selected{% endif %}>بابل</option>
                            <option value="كربلاء" {% if contract_data.get('car_city') == 'كربلاء' %}selected{% endif %}>كربلاء</option>
                            <option value="النجف" {% if contract_data.get('car_city') == 'النجف' %}selected{% endif %}>النجف</option>
                            <option value="ديالى" {% if contract_data.get('car_city') == 'ديالى' %}selected{% endif %}>ديالى</option>
                            <option value="صلاح الدين" {% if contract_data.get('car_city') == 'صلاح الدين' %}selected{% endif %}>صلاح الدين</option>
                            <option value="كركوك" {% if contract_data.get('car_city') == 'كركوك' %}selected{% endif %}>كركوك</option>
                            <option value="أربيل" {% if contract_data.get('car_city') == 'أربيل' %}selected{% endif %}>أربيل</option>
                            <option value="دهوك" {% if contract_data.get('car_city') == 'دهوك' %}selected{% endif %}>دهوك</option>
                            <option value="السليمانية" {% if contract_data.get('car_city') == 'السليمانية' %}selected{% endif %}>السليمانية</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="sin_num">رقم السند:</label>
                        <input type="text" id="sin_num" name="sin_num" value="{{ contract_data.get('sin_num', '') }}">
                    </div>
                    <div class="form-group">
                        <!-- حقل فارغ لتوازن التخطيط -->
                    </div>
                </div>
            </div>

            <div class="divider"></div>

            <!-- المعلومات المالية -->
            <h2 class="form-heading">المعلومات المالية</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="currency_type">نوع العملة:</label>
                        <select id="currency_type" name="currency_type" onchange="updateAmountDisplays()">
                            <option value="IQD" {% if contract_data.get('currency_type') == 'IQD' %}selected{% endif %}>دينار عراقي (IQD)</option>
                            <option value="USD" {% if contract_data.get('currency_type') == 'USD' %}selected{% endif %}>دولار أمريكي (USD)</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="mony_num">المبلغ الواصل:</label>
                        <input type="number" id="mony_num" name="mony_num" value="{% if contract_data.get('mony_num') %}{{ contract_data.get('mony_num', '').replace(',', '').replace('$', '').split()[0] }}{% endif %}" oninput="updateAmountDisplays()" required>
                        <div id="mony_display" class="amount-display"></div>
                    </div>
                    <div class="form-group">
                        <label for="badal_num">سعر السيارة:</label>
                        <input type="number" id="badal_num" name="badal_num" value="{% if contract_data.get('badal_num') %}{{ contract_data.get('badal_num', '').replace(',', '').replace('$', '').split()[0] }}{% endif %}" oninput="updateAmountDisplays()" required>
                        <div id="badal_display" class="amount-display"></div>
                    </div>

                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>المبلغ الباقي:</label>
                        <div id="remaining_display" class="amount-display" style="background-color: #fff3cd; border-color: #ffeaa7;"></div>
                    </div>
                </div>
                <div class="form-row" id="remaining_notes_row" style="display: none;">
                    <div class="form-group">
                        <label for="m1">ملاحظات الباقي:</label>
                        <input type="text" name="m1" id="m1" dir="rtl" placeholder="أدخل ملاحظات حول المبلغ الباقي..." value="{{ contract_data.get('m1', '') }}">
                    </div>
                </div>
            </div>

            <div class="divider"></div>

            <!-- الملاحظات -->
            <h2 class="form-heading">الملاحظات</h2>
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="note_a">ملاحظة أ:</label>
                        <input type="text" id="note_a" name="note_a" value="{{ contract_data.get('note_a', '') }}">
                    </div>
                    <div class="form-group">
                        <label for="note_b">ملاحظة ب:</label>
                        <input type="text" id="note_b" name="note_b" value="{{ contract_data.get('note_b', '') }}">
                    </div>
                </div>
            </div>

            <div class="divider"></div>

            <!-- الصور -->
            <h2 class="form-heading">صور العقد</h2>
            <div class="cameras-section">
                <div class="camera-group">
                    <h3>صورة المشتري</h3>
                    <div class="camera-container">
                        <video id="video-buyer" autoplay style="display: none;"></video>
                        <div class="photo-preview" id="photo-box-buyer">
                            {% if images.get('sellerPhoto') %}
                                <img src="data:image/png;base64,{{ images.get('sellerPhoto') }}" alt="صورة البائع">
                            {% else %}
                                <span style="color: #666;">لا توجد صورة</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="camera-buttons">
                        <button type="button" id="capture-btn-buyer" onclick="capturePhoto('buyer')" class="capture-btn">📷 التقاط صورة البائع</button>
                        <button type="button" id="retake-btn-buyer" onclick="retakePhoto('buyer')" class="capture-btn" style="display: none;">إعادة التقاط الصورة</button>
                        <button type="button" class="btn btn-secondary" onclick="removeSellerPhoto()">🗑️ حذف الصورة</button>
                    </div>
                </div>

                <div class="camera-group">
                    <h3>صورة البائع</h3>
                    <div class="camera-container">
                        <video id="video-seller" autoplay style="display: none;"></video>
                        <div class="photo-preview" id="photo-box-seller">
                            {% if images.get('buyerPhoto') %}
                                <img src="data:image/png;base64,{{ images.get('buyerPhoto') }}" alt="صورة المشتري">
                            {% else %}
                                <span style="color: #666;">لا توجد صورة</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="camera-buttons">
                        <button type="button" id="capture-btn-seller" onclick="capturePhoto('seller')" class="capture-btn">📷 التقاط صورة المشتري</button>
                        <button type="button" id="retake-btn-seller" onclick="retakePhoto('seller')" class="capture-btn" style="display: none;">إعادة التقاط الصورة</button>
                        <button type="button" class="btn btn-secondary" onclick="removeBuyerPhoto()">🗑️ حذف الصورة</button>
                    </div>
                </div>
            </div>

            <!-- حقول مخفية للصور -->
            <input type="hidden" id="sellerPhoto" name="sellerPhoto" value="{{ images.get('sellerPhoto', '') }}">
            <input type="hidden" id="buyerPhoto" name="buyerPhoto" value="{{ images.get('buyerPhoto', '') }}">

            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <button type="submit" class="btn btn-success" style="font-size: 16px; padding: 12px 30px;">💾 حفظ التعديلات</button>
                <button type="button" class="btn btn-primary" onclick="previewContract()" style="font-size: 16px; padding: 12px 30px;">👁️ معاينة العقد</button>
                <a href="/contracts" class="btn btn-secondary" style="font-size: 16px; padding: 12px 30px;">❌ إلغاء</a>
            </div>
        </form>

        <!-- سجل التعديلات السابقة -->
        {% if modifications_history and modifications_history|length > 0 %}
        <div class="modifications-history" style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;">
            <h3 style="color: #007bff; margin-bottom: 15px; text-align: center;">📋 سجل التعديلات السابقة</h3>
            <div style="max-height: 300px; overflow-y: auto;">
                {% for modification in modifications_history %}
                <div style="background: white; padding: 10px; margin-bottom: 8px; border-radius: 5px; border-left: 4px solid #007bff;">
                    <div style="font-size: 14px; color: #333;">
                        <strong>{{ modification.field }}:</strong>
                        تم التغيير من "<span style="color: #dc3545;">{{ modification.old_value }}</span>"
                        إلى "<span style="color: #28a745;">{{ modification.new_value }}</span>"
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        📅 {{ modification.timestamp }} | 👤 {{ modification.modified_by }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>

    <script>
        // متغيرات للكاميرا
        let sellerStream = null;
        let buyerStream = null;

        // تحديث عرض المبالغ
        function updateAmountDisplays() {
            const currencyType = document.getElementById('currency_type').value;
            const badalNum = parseInt(document.getElementById('badal_num').value) || 0;
            const monyNum = parseInt(document.getElementById('mony_num').value) || 0;
            const remainingNum = badalNum - monyNum;

            // تحديث عرض سعر السيارة
            updateAmountDisplay('badal_display', badalNum, currencyType);

            // تحديث عرض المبلغ الواصل
            updateAmountDisplay('mony_display', monyNum, currencyType);

            // تحديث عرض المبلغ الباقي
            updateAmountDisplay('remaining_display', remainingNum, currencyType);

            // إدارة حقل ملاحظات الباقي
            const m1Field = document.getElementById('m1');
            const remainingNotesRow = document.getElementById('remaining_notes_row');

            if (remainingNum > 0) {
                // إظهار الحقل إذا كان الباقي أكبر من 0
                remainingNotesRow.style.display = 'block';
                m1Field.disabled = false;
            } else {
                // إخفاء الحقل وإفراغه إذا كان الباقي = 0
                remainingNotesRow.style.display = 'none';
                m1Field.value = '';
                m1Field.disabled = true;
            }
        }

        async function updateAmountDisplay(elementId, amount, currencyType) {
            const element = document.getElementById(elementId);
            if (!element) return;

            const formattedNumber = formatNumberWithCurrency(amount, currencyType);

            // عرض الرقم المنسق فوراً
            element.innerHTML = `
                <div class="formatted-number">${formattedNumber}</div>
                <div class="arabic-text">جاري التحويل...</div>
            `;

            // الحصول على النص العربي من الخادم
            try {
                const response = await fetch('/api/convert_to_arabic', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        number: amount,
                        currency_type: currencyType
                    })
                });

                const data = await response.json();
                if (data.success) {
                    element.innerHTML = `
                        <div class="formatted-number">${formattedNumber}</div>
                        <div class="arabic-text">${data.arabic_text}</div>
                    `;
                } else {
                    element.innerHTML = `
                        <div class="formatted-number">${formattedNumber}</div>
                        <div class="arabic-text">خطأ في التحويل</div>
                    `;
                }
            } catch (error) {
                console.error('Error converting to Arabic:', error);
                element.innerHTML = `
                    <div class="formatted-number">${formattedNumber}</div>
                    <div class="arabic-text">خطأ في الاتصال</div>
                `;
            }
        }

        function formatNumberWithCurrency(number, currencyType) {
            const formatted = number.toLocaleString();
            return currencyType === 'USD' ? `$${formatted}` : `${formatted}`;
        }



        // متغيرات للصور
        let buyerPhoto = null;
        let sellerPhoto = null;

        // بدء الفيديو
        function startVideo(type) {
            // إيقاف الفيديو السابق إذا كان موجوداً
            if (type === 'buyer' && buyerStream) {
                buyerStream.getTracks().forEach(track => track.stop());
                buyerStream = null;
            }
            if (type === 'seller' && sellerStream) {
                sellerStream.getTracks().forEach(track => track.stop());
                sellerStream = null;
            }

            if (type === 'buyer') {
                navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    }
                })
                .then(function (stream) {
                    buyerStream = stream;
                    const video = document.getElementById('video-buyer');
                    video.srcObject = stream;
                    video.play();

                    // تحديث نص الزر عندما يكون الفيديو جاهزاً
                    video.onloadedmetadata = function() {
                        const captureBtn = document.getElementById('capture-btn-buyer');
                        captureBtn.textContent = '📸 التقاط صورة البائع';
                    };
                })
                .catch(function (err) {
                    console.error("خطأ في الوصول إلى الكاميرا:", err);
                    alert("حدث خطأ في الوصول إلى الكاميرا: " + err.message);
                    // إخفاء الفيديو وإظهار رسالة خطأ
                    document.getElementById('video-buyer').style.display = 'none';
                    document.getElementById('photo-box-buyer').innerHTML = '<span style="color: red;">خطأ في الوصول إلى الكاميرا</span>';
                });
            }

            if (type === 'seller') {
                navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    }
                })
                .then(function (stream) {
                    sellerStream = stream;
                    const video = document.getElementById('video-seller');
                    video.srcObject = stream;
                    video.play();

                    // تحديث نص الزر عندما يكون الفيديو جاهزاً
                    video.onloadedmetadata = function() {
                        const captureBtn = document.getElementById('capture-btn-seller');
                        captureBtn.textContent = '📸 التقاط صورة المشتري';
                    };
                })
                .catch(function (err) {
                    console.error("خطأ في الوصول إلى الكاميرا:", err);
                    alert("حدث خطأ في الوصول إلى الكاميرا: " + err.message);
                    // إخفاء الفيديو وإظهار رسالة خطأ
                    document.getElementById('video-seller').style.display = 'none';
                    document.getElementById('photo-box-seller').innerHTML = '<span style="color: red;">خطأ في الوصول إلى الكاميرا</span>';
                });
            }
        }

        // التقاط صورة - تعريف في النطاق العام
        window.capturePhoto = function(type) {
            console.log('capturePhoto called with type:', type);

            let videoElement, photoBox, captureBtn;

            if (type === 'buyer') {
                videoElement = document.getElementById('video-buyer');
                photoBox = document.getElementById('photo-box-buyer');
                captureBtn = document.getElementById('capture-btn-buyer');
            } else if (type === 'seller') {
                videoElement = document.getElementById('video-seller');
                photoBox = document.getElementById('photo-box-seller');
                captureBtn = document.getElementById('capture-btn-seller');
            }

            console.log('Elements found:', {
                videoElement: !!videoElement,
                photoBox: !!photoBox,
                captureBtn: !!captureBtn,
                videoDisplay: videoElement?.style.display,
                hasStream: !!videoElement?.srcObject
            });

            // إذا كان الفيديو مخفي أو لا يوجد stream، ابدأ الكاميرا أولاً
            if (videoElement.style.display === 'none' || !videoElement.srcObject) {
                console.log('Starting camera for', type);
                // إظهار الفيديو وبدء الكاميرا
                videoElement.style.display = 'block';
                photoBox.style.display = 'none';
                startVideo(type);

                // تغيير نص الزر إلى "التقاط الصورة"
                captureBtn.textContent = type === 'buyer' ? '📸 التقاط صورة البائع' : '📸 التقاط صورة المشتري';
                return;
            }

            // التحقق من أن الفيديو جاهز للتقاط
            if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
                alert('الكاميرا غير جاهزة بعد. يرجى المحاولة مرة أخرى.');
                return;
            }

            console.log('Capturing photo for', type);

            // إذا كان الفيديو يعمل، التقط الصورة
            let canvas = document.createElement('canvas');
            let ctx = canvas.getContext('2d');

            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;
            ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

            let photoData = canvas.toDataURL('image/jpeg').split(',')[1];

            // Hide video and show photo preview
            videoElement.style.display = 'none';
            photoBox.style.display = 'block';

            let photoHtml = `<img src="data:image/jpeg;base64,${photoData}" alt="${type} Photo">`;
            photoBox.innerHTML = photoHtml;

            // Hide capture button and show retake button
            captureBtn.style.display = 'none';
            document.getElementById(`retake-btn-${type}`).style.display = 'inline-block';

            // حفظ الصورة في المتغير والحقل المخفي
            if (type === 'buyer') {
                buyerPhoto = photoData;
                document.getElementById('sellerPhoto').value = photoData;  // البائع = buyer
            } else if (type === 'seller') {
                sellerPhoto = photoData;
                document.getElementById('buyerPhoto').value = photoData;   // المشتري = seller
            }

            console.log('Photo captured and saved for', type);

            // إيقاف الفيديو
            if (type === 'buyer' && buyerStream) {
                buyerStream.getTracks().forEach(track => track.stop());
                buyerStream = null;
            } else if (type === 'seller' && sellerStream) {
                sellerStream.getTracks().forEach(track => track.stop());
                sellerStream = null;
            }
        };

        // إعادة التقاط الصورة - تعريف في النطاق العام
        window.retakePhoto = function(type) {
            let videoElement = document.getElementById(`video-${type}`);
            let photoBox = document.getElementById(`photo-box-${type}`);
            let captureBtn = document.getElementById(`capture-btn-${type}`);

            // Show video and hide photo preview
            videoElement.style.display = 'block';
            photoBox.style.display = 'none';
            captureBtn.style.display = 'block';
            captureBtn.textContent = type === 'buyer' ? '📸 التقاط صورة البائع' : '📸 التقاط صورة المشتري';
            document.getElementById(`retake-btn-${type}`).style.display = 'none';

            // Restart video stream
            startVideo(type);
        };

        // حذف صورة البائع - تعريف في النطاق العام
        window.removeSellerPhoto = function() {
            console.log('removeSellerPhoto called');

            // مسح البيانات
            document.getElementById('sellerPhoto').value = '';
            document.getElementById('photo-box-buyer').innerHTML = '<span style="color: #666;">لا توجد صورة</span>';
            buyerPhoto = null;

            // إعادة تعيين الأزرار
            const captureBtn = document.getElementById('capture-btn-buyer');
            const retakeBtn = document.getElementById('retake-btn-buyer');

            if (captureBtn && retakeBtn) {
                captureBtn.style.display = 'inline-block';
                captureBtn.textContent = '📷 التقاط صورة البائع';
                retakeBtn.style.display = 'none';

                console.log('Seller photo removed and buttons reset');
            } else {
                console.error('Could not find capture or retake buttons for buyer');
            }

            // إيقاف وإخفاء الفيديو
            if (buyerStream) {
                buyerStream.getTracks().forEach(track => track.stop());
                buyerStream = null;
            }
            document.getElementById('video-buyer').style.display = 'none';
            document.getElementById('photo-box-buyer').style.display = 'block';
        };

        // حذف صورة المشتري - تعريف في النطاق العام
        window.removeBuyerPhoto = function() {
            console.log('removeBuyerPhoto called');

            // مسح البيانات
            document.getElementById('buyerPhoto').value = '';
            document.getElementById('photo-box-seller').innerHTML = '<span style="color: #666;">لا توجد صورة</span>';
            sellerPhoto = null;

            // إعادة تعيين الأزرار
            const captureBtn = document.getElementById('capture-btn-seller');
            const retakeBtn = document.getElementById('retake-btn-seller');

            if (captureBtn && retakeBtn) {
                captureBtn.style.display = 'inline-block';
                captureBtn.textContent = '📷 التقاط صورة المشتري';
                retakeBtn.style.display = 'none';

                console.log('Buyer photo removed and buttons reset');
            } else {
                console.error('Could not find capture or retake buttons for seller');
            }

            // إيقاف وإخفاء الفيديو
            if (sellerStream) {
                sellerStream.getTracks().forEach(track => track.stop());
                sellerStream = null;
            }
            document.getElementById('video-seller').style.display = 'none';
            document.getElementById('photo-box-seller').style.display = 'block';
        };

        // معاينة العقد - تعريف في النطاق العام
        window.previewContract = function() {
            const form = document.getElementById('editContractForm');
            const formData = new FormData(form);

            // إظهار رسالة تحميل
            const loadingMsg = document.createElement('div');
            loadingMsg.id = 'preview-loading-message';
            loadingMsg.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px;
                border-radius: 10px;
                z-index: 10000;
                text-align: center;
                font-size: 16px;
            `;
            loadingMsg.innerHTML = `
                <div>🔄 جاري إنشاء PDF للمعاينة...</div>
                <div style="margin-top: 10px; font-size: 14px;">يرجى الانتظار</div>
            `;
            document.body.appendChild(loadingMsg);

            // إرسال طلب إنشاء PDF للمعاينة
            fetch('/api/preview_contract_pdf', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // إزالة رسالة التحميل
                const loadingMsg = document.getElementById('preview-loading-message');
                if (loadingMsg) {
                    loadingMsg.remove();
                }

                if (data.success) {
                    // فتح PDF في تبويب جديد
                    const pdfUrl = `/pdf/${data.pdf_filename}`;
                    window.open(pdfUrl, '_blank');
                } else {
                    alert('❌ فشل في إنشاء PDF للمعاينة: ' + (data.error || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                // إزالة رسالة التحميل
                const loadingMsg = document.getElementById('preview-loading-message');
                if (loadingMsg) {
                    loadingMsg.remove();
                }

                console.error('خطأ في معاينة العقد:', error);

                // تحديد نوع الخطأ وإظهار رسالة مناسبة
                let errorMessage = 'حدث خطأ في معاينة العقد';
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage = 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت';
                } else if (error.name === 'AbortError') {
                    errorMessage = 'تم إلغاء العملية بسبب انتهاء المهلة الزمنية';
                } else if (error.message) {
                    errorMessage = error.message;
                }

                alert('❌ ' + errorMessage);
            });
        };

        // تحديث العرض عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // اختبار توفر الدوال
            console.log('Functions available:', {
                capturePhoto: typeof window.capturePhoto,
                retakePhoto: typeof window.retakePhoto,
                removeSellerPhoto: typeof window.removeSellerPhoto,
                removeBuyerPhoto: typeof window.removeBuyerPhoto,
                previewContract: typeof window.previewContract
            });

            updateAmountDisplays();

            // إعداد الصور الموجودة
            const sellerPhotoValue = document.getElementById('sellerPhoto').value;
            const buyerPhotoValue = document.getElementById('buyerPhoto').value;

            if (sellerPhotoValue) {
                buyerPhoto = sellerPhotoValue;  // البائع = buyer
                document.getElementById('capture-btn-buyer').style.display = 'none';
                document.getElementById('retake-btn-buyer').style.display = 'inline-block';
                document.getElementById('video-buyer').style.display = 'none';
                document.getElementById('photo-box-buyer').style.display = 'block';
            } else {
                // إذا لم توجد صورة، تأكد من إظهار الأزرار الصحيحة
                document.getElementById('capture-btn-buyer').style.display = 'inline-block';
                document.getElementById('retake-btn-buyer').style.display = 'none';
                document.getElementById('video-buyer').style.display = 'none';
                document.getElementById('photo-box-buyer').style.display = 'block';
            }

            if (buyerPhotoValue) {
                sellerPhoto = buyerPhotoValue;  // المشتري = seller
                document.getElementById('capture-btn-seller').style.display = 'none';
                document.getElementById('retake-btn-seller').style.display = 'inline-block';
                document.getElementById('video-seller').style.display = 'none';
                document.getElementById('photo-box-seller').style.display = 'block';
            } else {
                // إذا لم توجد صورة، تأكد من إظهار الأزرار الصحيحة
                document.getElementById('capture-btn-seller').style.display = 'inline-block';
                document.getElementById('retake-btn-seller').style.display = 'none';
                document.getElementById('video-seller').style.display = 'none';
                document.getElementById('photo-box-seller').style.display = 'block';
            }
        });

        // معالجة إرسال النموذج
        document.getElementById('editContractForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // إرسال البيانات
            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حفظ التعديلات بنجاح!');
                    window.location.href = '/contracts';
                } else {
                    alert('خطأ في حفظ التعديلات: ' + (data.error || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('خطأ في حفظ التعديلات:', error);

                // تحديد نوع الخطأ وإظهار رسالة مناسبة
                let errorMessage = 'خطأ في الاتصال بالخادم';
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage = 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى';
                } else if (error.name === 'AbortError') {
                    errorMessage = 'تم إلغاء العملية بسبب انتهاء المهلة الزمنية. يرجى المحاولة مرة أخرى';
                } else if (error.message) {
                    errorMessage = error.message;
                }

                alert('❌ ' + errorMessage);
            });
        });
    </script>
        </div>
    </div>
</body>
</html>
