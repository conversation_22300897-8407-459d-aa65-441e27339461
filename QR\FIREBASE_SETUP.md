# إعداد Firebase للموقع

## نظرة عامة

تم دمج Firebase مع الموقع بطريقة آمنة تحافظ على حماية البيانات الحساسة. النظام يدعم:

- **Firebase Firestore**: لتخزين بيانات العقود
- **Firebase Authentication**: للمصادقة الآمنة
- **Firebase Storage**: لتخزين الملفات
- **Firebase Analytics**: لتتبع الاستخدام

## الأمان

### المتغيرات الحساسة (Server-Side Only)
هذه المتغيرات محفوظة في ملف `.env` ولا تظهر أبداً في الجانب الأمامي:
- `FIREBASE_PRIVATE_KEY`: المفتاح الخاص
- `FIREBASE_CLIENT_EMAIL`: بريد الخدمة
- `FIREBASE_CLIENT_ID`: معرف العميل
- جميع معلومات Service Account

### المتغيرات العامة (Client-Side)
هذه المتغيرات آمنة للكشف في المتصفح:
- `apiKey`: مفتاح API العام
- `authDomain`: نطاق المصادقة
- `projectId`: معرف المشروع
- `storageBucket`: حاوية التخزين

## بنية الملفات

```
├── .env                          # المتغيرات الحساسة (محمي بـ .gitignore)
├── firebase_admin_config.py      # إعدادات Firebase للخادم
├── static/js/
│   ├── firebase-config.js        # إعدادات Firebase للجانب الأمامي (ES6)
│   └── firebase-simple.js        # إعدادات Firebase للاستخدام المباشر
├── templates/base.html           # قالب HTML مع Firebase SDK
└── app.py                        # التطبيق الرئيسي مع دمج Firebase
```

## كيفية العمل

### 1. تحميل البيانات
```python
# الأولوية: Firebase Firestore
contract = get_contract_from_firebase(uuid)

# البديل: الملف المحلي
if not contract:
    contract = get_contract_from_data(uuid)
```

### 2. المصادقة
```javascript
// في الجانب الأمامي
firebase.auth().signInWithEmailAndPassword(email, password)
    .then(userCredential => {
        return userCredential.user.getIdToken();
    })
    .then(idToken => {
        return verifyTokenWithServer(idToken);
    });
```

```python
# في الخادم
decoded_token = verify_token(id_token)
if decoded_token:
    user_uid = decoded_token['uid']
```

## API Endpoints

### `/api/firebase-config`
- **Method**: GET
- **Description**: إرجاع إعدادات Firebase العامة للجانب الأمامي
- **Response**: JSON مع المتغيرات العامة فقط

### `/api/verify-token`
- **Method**: POST
- **Body**: `{"idToken": "firebase_id_token"}`
- **Description**: التحقق من رمز Firebase
- **Response**: معلومات المستخدم أو خطأ

### `/health`
- **Method**: GET
- **Description**: فحص صحة النظام
- **Response**: حالة النظام + حالة Firebase

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. إعداد متغيرات البيئة
تأكد من وجود ملف `.env` مع جميع المتغيرات المطلوبة.

### 3. تشغيل التطبيق
```bash
python app.py
```

## استخدام Firebase في الجانب الأمامي

### تهيئة تلقائية
Firebase يتم تهيئته تلقائياً عند تحميل الصفحة:

```javascript
// Firebase متاح عالمياً بعد التحميل
firebase.auth()
firebase.firestore()
firebase.storage()
```

### مثال على المصادقة
```javascript
// تسجيل الدخول
firebase.auth().signInWithEmailAndPassword(email, password)
    .then(userCredential => {
        console.log('تم تسجيل الدخول بنجاح');
    })
    .catch(error => {
        console.error('خطأ في تسجيل الدخول:', error);
    });

// تسجيل الخروج
firebase.auth().signOut()
    .then(() => {
        console.log('تم تسجيل الخروج بنجاح');
    });
```

### مثال على قراءة البيانات
```javascript
// قراءة عقد من Firestore
firebase.firestore().collection('contracts')
    .where('uuid', '==', contractUuid)
    .get()
    .then(querySnapshot => {
        querySnapshot.forEach(doc => {
            console.log('بيانات العقد:', doc.data());
        });
    });
```

## الأمان والحماية

### 1. حماية المتغيرات الحساسة
- جميع المتغيرات الحساسة في `.env`
- `.env` محمي بـ `.gitignore`
- لا يتم إرسال أي متغيرات حساسة للجانب الأمامي

### 2. التحقق من الرموز
- جميع رموز Firebase يتم التحقق منها في الخادم
- استخدام Firebase Admin SDK للتحقق الآمن

### 3. حماية API
- Rate limiting على جميع endpoints
- التحقق من صحة البيانات
- تسجيل جميع العمليات

### 4. Headers الأمان
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Content-Security-Policy

## استكشاف الأخطاء

### Firebase غير متاح
إذا كان Firebase غير متاح، النظام يعمل بالملف المحلي:
```
[WARNING] Firebase Admin SDK not available
[INFO] Running in local file mode only
```

### خطأ في المصادقة
```
[ERROR] Error verifying token: Invalid token
```

### خطأ في الاتصال
```
[ERROR] Failed to get Firestore client
```

## ملاحظات مهمة

1. **لا تضع أبداً** المتغيرات الحساسة في الجانب الأمامي
2. **استخدم دائماً** التحقق من الرموز في الخادم
3. **احتفظ بنسخة احتياطية** من ملف `.env`
4. **راقب logs** للتأكد من عدم وجود تسريبات أمنية

## الدعم

للمساعدة أو الاستفسارات، راجع:
- [Firebase Documentation](https://firebase.google.com/docs)
- [Flask Documentation](https://flask.palletsprojects.com/)
- ملفات logs للتشخيص
