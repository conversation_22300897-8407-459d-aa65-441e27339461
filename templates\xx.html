<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول DOCX إلى PDF</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .title {
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: bold;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px 20px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
            background: #f8f9ff;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #666;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .file-input {
            display: none;
        }

        .selected-file {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #2e7d32;
            font-weight: bold;
        }

        .convert-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 20px;
        }

        .convert-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .convert-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .progress {
            display: none;
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            display: none;
        }

        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #4caf50;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 2px solid #f44336;
        }

        .status.info {
            background: #e3f2fd;
            color: #1565c0;
            border: 2px solid #2196f3;
        }

        .download-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .quality-report {
            background: #f5f5f5;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            text-align: right;
            font-size: 14px;
            color: #555;
            border-right: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔄 محول DOCX إلى PDF</h1>
        
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📄</div>
            <div class="upload-text">اضغط هنا أو اسحب ملف DOCX</div>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
                الحد الأقصى: 10 ميجابايت
            </div>
            <input type="file" id="fileInput" class="file-input" accept=".docx" />
        </div>

        <div id="selectedFile" class="selected-file" style="display: none;"></div>

        <button id="convertBtn" class="convert-btn" disabled>
            🚀 تحويل إلى PDF
        </button>

        <div id="progress" class="progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div style="margin-top: 10px; color: #666;">جاري التحويل...</div>
        </div>

        <div id="status" class="status"></div>
        
        <div id="qualityReport" class="quality-report" style="display: none;"></div>
    </div>

    <script type="module">
        import { Client, handle_file } from "https://cdn.jsdelivr.net/npm/@gradio/client/dist/index.js";

        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const selectedFile = document.getElementById('selectedFile');
        const convertBtn = document.getElementById('convertBtn');
        const progress = document.getElementById('progress');
        const progressFill = document.getElementById('progressFill');
        const status = document.getElementById('status');
        const qualityReport = document.getElementById('qualityReport');

        let selectedFileData = null;

        // Handle file selection
        uploadArea.addEventListener('click', () => fileInput.click());
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelection(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelection(e.target.files[0]);
            }
        });

        function handleFileSelection(file) {
            if (!file.name.toLowerCase().endsWith('.docx')) {
                showStatus('يرجى اختيار ملف DOCX فقط', 'error');
                return;
            }

            if (file.size > 10 * 1024 * 1024) { // 10MB limit
                showStatus('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت', 'error');
                return;
            }

            selectedFileData = file;
            selectedFile.textContent = `✅ تم اختيار: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} ميجابايت)`;
            selectedFile.style.display = 'block';
            convertBtn.disabled = false;
            hideStatus();
        }

        convertBtn.addEventListener('click', async () => {
            if (!selectedFileData) return;

            try {
                convertBtn.disabled = true;
                showProgress();
                showStatus('جاري الاتصال بالخادم...', 'info');

                // Connect to Gradio API
                const app = await Client.connect("kalhdrawi/pdf");

                showStatus('جاري تحويل الملف...', 'info');
                updateProgress(50);

                // Send file for conversion using handle_file
                const result = await app.predict("/predict", [handle_file(selectedFileData)]);

                updateProgress(100);
                hideProgress();

                console.log('API Response:', result); // للتشخيص

                if (result.data && result.data.length >= 2) {
                    const [pdfFileInfo, qualityStatus] = result.data;

                    console.log('PDF File Info:', pdfFileInfo); // للتشخيص
                    console.log('Quality Status:', qualityStatus); // للتشخيص

                    if (pdfFileInfo && pdfFileInfo.url) {
                        // Fetch the actual PDF file from the URL
                        const pdfResponse = await fetch(pdfFileInfo.url);
                        const pdfBlob = await pdfResponse.blob();

                        // Create download link and auto-open
                        const url = URL.createObjectURL(pdfBlob);

                        // Open in new window for viewing/printing
                        const newWindow = window.open(url, '_blank');
                        if (!newWindow) {
                            // Fallback: create download link
                            const downloadLink = document.createElement('a');
                            downloadLink.href = url;
                            downloadLink.download = selectedFileData.name.replace('.docx', '.pdf');
                            downloadLink.textContent = '📥 تحميل ملف PDF';
                            downloadLink.className = 'download-btn';

                            showStatus('تم التحويل بنجاح!', 'success');
                            status.appendChild(document.createElement('br'));
                            status.appendChild(downloadLink);
                        } else {
                            showStatus('تم التحويل بنجاح! تم فتح الملف في نافذة جديدة', 'success');
                        }

                        // Show quality report
                        if (qualityStatus) {
                            qualityReport.innerHTML = `<strong>📊 تقرير الجودة:</strong><br>${qualityStatus}`;
                            qualityReport.style.display = 'block';
                        }

                        // Clean up
                        setTimeout(() => URL.revokeObjectURL(url), 60000);
                    } else {
                        showStatus('حدث خطأ في التحويل. لم يتم إرجاع ملف PDF صالح', 'error');
                        console.error('Invalid PDF file info:', pdfFileInfo);
                    }
                } else {
                    showStatus('استجابة غير متوقعة من الخادم', 'error');
                    console.error('Unexpected response structure:', result);
                }

            } catch (error) {
                console.error('Conversion error:', error);
                hideProgress();
                showStatus(`خطأ في التحويل: ${error.message}`, 'error');
            } finally {
                convertBtn.disabled = false;
            }
        });

        function showProgress() {
            progress.style.display = 'block';
            updateProgress(25);
        }

        function hideProgress() {
            progress.style.display = 'none';
            updateProgress(0);
        }

        function updateProgress(percent) {
            progressFill.style.width = percent + '%';
        }

        function showStatus(message, type) {
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        function hideStatus() {
            status.style.display = 'none';
            qualityReport.style.display = 'none';
        }
    </script>
</body>
</html>
