# دليل Firebase السريع

## 🚀 البدء السريع

### 1. التثبيت
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# أو تشغيل سكريبت التثبيت
./install.sh
```

### 2. إعداد Firebase
```bash
# التأكد من وجود ملف .env مع متغيرات Firebase
# الملف موجود بالفعل مع القيم الصحيحة
```

### 3. اختبار Firebase
```bash
# اختبار الاتصال
python test_firebase.py
```

### 4. تشغيل الموقع
```bash
# تشغيل التطبيق
python app.py
```

## 🔧 الأوامر المفيدة

### اختبار Firebase
```bash
python test_firebase.py
```

### إضافة عقد تجريبي
```bash
python add_contract_to_firebase.py add
```

### نقل العقود المحلية إلى Firebase
```bash
python add_contract_to_firebase.py migrate
```

### عرض العقود في Firebase
```bash
python add_contract_to_firebase.py list
```

## 🌐 الروابط المهمة

### API Endpoints
- `GET /health` - فحص صحة النظام
- `GET /api/firebase-config` - إعدادات Firebase العامة
- `POST /api/verify-token` - التحقق من رمز المصادقة
- `GET /contract/<uuid>` - عرض عقد

### مثال على الاستخدام
```bash
# فحص صحة النظام
curl http://localhost:21226/health

# الحصول على إعدادات Firebase
curl http://localhost:21226/api/firebase-config

# عرض عقد (يحتاج UUID صحيح)
curl http://localhost:21226/contract/12345678-1234-1234-1234-123456789012
```

## 🔐 الأمان

### ✅ آمن (يمكن كشفه)
- `apiKey`
- `authDomain`
- `projectId`
- `storageBucket`
- `messagingSenderId`
- `appId`

### ❌ حساس (محمي في .env)
- `private_key`
- `client_email`
- `client_id`
- `private_key_id`

## 🐛 استكشاف الأخطاء

### Firebase غير متاح
```
[WARNING] Firebase Admin SDK not available
```
**الحل**: تحقق من ملف .env ومن تثبيت firebase-admin

### خطأ في المصادقة
```
[ERROR] Error verifying token: Invalid token
```
**الحل**: تحقق من صحة الرمز المرسل

### خطأ في الاتصال
```
[ERROR] Failed to get Firestore client
```
**الحل**: تحقق من اتصال الإنترنت وصحة المتغيرات

## 📁 بنية الملفات

```
├── .env                          # المتغيرات الحساسة ⚠️
├── firebase_admin_config.py      # إعدادات الخادم
├── static/js/
│   ├── firebase-config.js        # إعدادات الجانب الأمامي
│   └── firebase-simple.js        # إعدادات بسيطة
├── templates/base.html           # قالب مع Firebase
├── app.py                        # التطبيق الرئيسي
├── test_firebase.py              # اختبار Firebase
├── add_contract_to_firebase.py   # إدارة العقود
└── FIREBASE_SETUP.md             # دليل مفصل
```

## 🎯 نصائح سريعة

1. **دائماً** اختبر Firebase قبل التشغيل:
   ```bash
   python test_firebase.py
   ```

2. **لا تنس** التحقق من logs:
   ```bash
   tail -f *.log
   ```

3. **استخدم** الأوامر المساعدة:
   ```bash
   python add_contract_to_firebase.py list
   ```

4. **راقب** endpoint الصحة:
   ```bash
   curl http://localhost:21226/health
   ```

## 🆘 المساعدة

- **للتفاصيل الكاملة**: راجع `FIREBASE_SETUP.md`
- **للمشاكل العامة**: راجع `README.md`
- **للاختبار**: شغل `python test_firebase.py`
- **للعقود**: شغل `python add_contract_to_firebase.py list`
