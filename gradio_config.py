# إعدادات Gradio API لتحويل Word إلى PDF

# معرف Gradio Space على Hugging Face (نفس المستخدم في xx.html)
GRADIO_SPACE_ID = "kalhdrawi/pdf"

# مهلة انتظار الطلب (بالثواني)
GRADIO_TIMEOUT = 120

# إعدادات إضافية
GRADIO_SETTINGS = {
    'max_retries': 3,  # عدد المحاولات في حالة الفشل
    'retry_delay': 5,  # تأخير بين المحاولات (بالثواني)
    'endpoint': "/predict",  # نقطة النهاية للتنبؤ
}

# رسائل الخطأ
ERROR_MESSAGES = {
    'connection_error': 'فشل في الاتصال بـ Gradio Space',
    'timeout_error': 'انتهت مهلة انتظار Gradio Space',
    'invalid_response': 'استجابة غير صالحة من Gradio Space',
    'file_not_found': 'ملف Word غير موجود',
    'empty_pdf': 'ملف PDF فارغ أو غير صالح',
    'space_unavailable': 'Gradio Space غير متاح حالياً',
}

def get_gradio_space_id():
    """الحصول على معرف Gradio Space"""
    return GRADIO_SPACE_ID

def get_timeout():
    """الحصول على مهلة الانتظار"""
    return GRADIO_TIMEOUT

def get_error_message(error_type):
    """الحصول على رسالة خطأ محددة"""
    return ERROR_MESSAGES.get(error_type, 'خطأ غير معروف')
