#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Application Secure Starter
تشغيل تطبيق QR الآمن

Windows-compatible runner without emoji characters.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def main():
    """تشغيل التطبيق الآمن"""
    try:
        print("QR Application - Secure Mode")
        print("=" * 40)
        
        # التحقق من وجود ملف .env
        if not os.path.exists('.env'):
            print("[ERROR] ملف .env غير موجود!")
            print("يرجى نسخ .env.example إلى .env وتعديل القيم")
            sys.exit(1)
        
        # استيراد التطبيق
        try:
            from app import app, init_firebase
        except ImportError as e:
            print(f"[ERROR] خطأ في استيراد التطبيق: {e}")
            print("تأكد من تثبيت جميع المكتبات المطلوبة:")
            print("pip install -r requirements.txt")
            sys.exit(1)
        
        # تهيئة Firebase
        print("تهيئة Firebase...")
        try:
            init_firebase()
            print("[SUCCESS] تم تهيئة Firebase بنجاح")
        except Exception as e:
            print(f"[WARNING] تحذير Firebase: {e}")
            print("سيتم استخدام الملف المحلي كبديل")
        
        # إعداد السيرفر
        port = int(os.environ.get('PORT', 21226))
        debug = os.environ.get('FLASK_ENV') == 'development'
        
        print(f"\nتشغيل السيرفر الآمن على المنفذ {port}")
        print(f"الرابط: http://localhost:{port}")
        print(f"وضع التطوير: {'مفعل' if debug else 'معطل'}")
        
        print("\n[SECURITY] الميزات الأمنية المفعلة:")
        print("- تشفير AES-256 للبيانات الحساسة")
        print("- مصادقة JWT مع حماية CSRF")
        print("- حماية من هجمات SQL Injection")
        print("- حماية من هجمات XSS")
        print("- Rate limiting متقدم")
        print("- مراقبة أمنية في الوقت الفعلي")
        print("- Security headers متقدمة")
        print("- حظر تلقائي للـ IPs المشبوهة")
        
        print("\n[INFO] ملفات السجلات:")
        print("- security.log: سجل الأحداث العامة")
        print("- security_events.log: سجل الأحداث الأمنية")
        
        print("\nبدء التشغيل...")
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=port,
            debug=debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n[INFO] تم إيقاف السيرفر الآمن")
        print("[SECURITY] جميع الجلسات الأمنية تم إنهاؤها بأمان")
    except Exception as e:
        print(f"\n[ERROR] خطأ في تشغيل التطبيق: {e}")
        print("يرجى مراجعة سجلات الأخطاء للمزيد من التفاصيل")
        sys.exit(1)

if __name__ == '__main__':
    main()
