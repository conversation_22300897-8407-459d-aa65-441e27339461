#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Application Final Runner
تشغيل تطبيق QR النهائي الآمن

Final secure runner for production deployment.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def main():
    """تشغيل التطبيق النهائي"""
    try:
        print("=" * 60)
        print("QR Application - SECURE PRODUCTION MODE")
        print("=" * 60)
        
        # التحقق من وجود ملف .env
        if not os.path.exists('.env'):
            print("[ERROR] ملف .env غير موجود!")
            print("يرجى نسخ .env.example إلى .env وتعديل القيم")
            sys.exit(1)
        
        print("[INFO] تحميل متغيرات البيئة...")
        
        # استيراد التطبيق
        try:
            print("[INFO] تحميل التطبيق الآمن...")
            from app import app, init_firebase
            print("[SUCCESS] تم تحميل التطبيق بنجاح")
        except ImportError as e:
            print(f"[ERROR] خطأ في استيراد التطبيق: {e}")
            print("تأكد من تثبيت جميع المكتبات المطلوبة:")
            print("pip install -r requirements.txt")
            sys.exit(1)
        
        # تهيئة Firebase
        print("[INFO] تهيئة Firebase...")
        try:
            init_firebase()
            print("[SUCCESS] تم تهيئة Firebase بنجاح")
        except Exception as e:
            print(f"[WARNING] تحذير Firebase: {e}")
            print("[INFO] سيتم استخدام الملف المحلي كبديل")
        
        # إعداد السيرفر
        port = int(os.environ.get('PORT', 21226))
        debug = os.environ.get('FLASK_ENV') == 'development'
        
        print("\n" + "=" * 60)
        print("SECURITY STATUS - حالة الأمان")
        print("=" * 60)
        print("[SECURITY] الميزات الأمنية المفعلة:")
        print("  ✓ تشفير AES-256 للبيانات الحساسة")
        print("  ✓ مصادقة JWT مع حماية CSRF")
        print("  ✓ حماية من هجمات SQL Injection")
        print("  ✓ حماية من هجمات XSS")
        print("  ✓ حماية من هجمات Command Injection")
        print("  ✓ Rate limiting متقدم")
        print("  ✓ مراقبة أمنية في الوقت الفعلي")
        print("  ✓ حظر تلقائي للـ IPs المشبوهة")
        print("  ✓ Security headers متقدمة")
        print("  ✓ فرض HTTPS في الإنتاج")
        print("  ✓ تسجيل شامل للأحداث الأمنية")
        
        print("\n[SECURITY] ملفات السجلات:")
        print("  - security.log: سجل الأحداث العامة")
        print("  - security_events.log: سجل الأحداث الأمنية")
        
        print("\n" + "=" * 60)
        print("SERVER CONFIGURATION - إعدادات السيرفر")
        print("=" * 60)
        print(f"[SERVER] المنفذ: {port}")
        print(f"[SERVER] الرابط: http://localhost:{port}")
        print(f"[SERVER] وضع التطوير: {'مفعل' if debug else 'معطل'}")
        print(f"[SERVER] الأمان: أقصى حماية")
        
        print("\n[INFO] للوصول إلى إحصائيات الأمان (للمدراء فقط):")
        print(f"  http://localhost:{port}/api/security/stats")
        
        print("\n" + "=" * 60)
        print("STARTING SECURE SERVER - بدء السيرفر الآمن")
        print("=" * 60)
        print("[INFO] جاري بدء التشغيل...")
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=port,
            debug=debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n" + "=" * 60)
        print("[INFO] تم إيقاف السيرفر الآمن")
        print("[SECURITY] جميع الجلسات الأمنية تم إنهاؤها بأمان")
        print("=" * 60)
    except Exception as e:
        print(f"\n[ERROR] خطأ في تشغيل التطبيق: {e}")
        print("يرجى مراجعة سجلات الأخطاء للمزيد من التفاصيل")
        sys.exit(1)

if __name__ == '__main__':
    main()
