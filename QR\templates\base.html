<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <title>{% block title %}عارض العقود{% endblock %}</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            line-height: 1.8;
        }
        
        .error-container {
            text-align: center;
            padding: 60px 20px;
        }
        
        .error-icon {
            font-size: 4em;
            color: #e74c3c;
            margin-bottom: 20px;
        }
        
        .error-title {
            font-size: 2em;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .error-message {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .security-notice {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 0.9em;
            color: #7f8c8d;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header, .content {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{% block header_title %}عارض العقود{% endblock %}</h1>
            <p>{% block header_subtitle %}نظام آمن لعرض العقود{% endblock %}</p>
        </div>
        
        <div class="content">
            {% block content %}{% endblock %}
        </div>
        
        <div class="security-notice">
            هذا النظام محمي بأعلى معايير الأمان. جميع الوصولات مسجلة ومراقبة.
        </div>
    </div>
    
    <!-- Firebase Configuration -->
    <script src="{{ url_for('static', filename='js/firebase-simple.js') }}"></script>

    <script>
        // Firebase initialization and utilities
        let firebaseInitialized = false;
        let currentUser = null;

        // Initialize Firebase when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load Firebase config from server
            fetch('/api/firebase-config')
                .then(response => response.json())
                .then(config => {
                    // Initialize Firebase with config from server
                    if (typeof firebase !== 'undefined') {
                        firebase.initializeApp(config);
                        firebaseInitialized = true;
                        console.log('Firebase initialized successfully');

                        // Set up auth state listener
                        firebase.auth().onAuthStateChanged(function(user) {
                            currentUser = user;
                            if (user) {
                                console.log('User signed in:', user.uid);
                                // User is signed in
                                handleUserSignedIn(user);
                            } else {
                                console.log('User signed out');
                                // User is signed out
                                handleUserSignedOut();
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading Firebase config:', error);
                });
        });

        // Handle user signed in
        function handleUserSignedIn(user) {
            // This function can be overridden in specific pages
            console.log('User authenticated:', user.email);
        }

        // Handle user signed out
        function handleUserSignedOut() {
            // This function can be overridden in specific pages
            console.log('User not authenticated');
        }

        // Utility function to verify token with server
        async function verifyTokenWithServer(idToken) {
            try {
                const response = await fetch('/api/verify-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ idToken: idToken })
                });

                if (response.ok) {
                    const data = await response.json();
                    return data;
                } else {
                    console.error('Token verification failed');
                    return null;
                }
            } catch (error) {
                console.error('Error verifying token:', error);
                return null;
            }
        }

        // Make functions available globally
        window.verifyTokenWithServer = verifyTokenWithServer;
        window.handleUserSignedIn = handleUserSignedIn;
        window.handleUserSignedOut = handleUserSignedOut;
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
