"""
وحدة تحويل الأرقام إلى نص عربي فصيح
Arabic Number to Text Converter Module

هذه الوحدة تقوم بتحويل الأرقام إلى نص باللغة العربية الفصحى
مع مراعاة قواعد النحو العربي الصحيحة للعدد والمعدود

المميزات:
- دعم الأرقام من 0 إلى 999,999,999,999 (تريليون)
- دعم المنازل العشرية للعملات
- التوافق النحوي الصحيح بين العدد والمعدود
- دعم العملات: الدينار العراقي (IQD) والدولار الأمريكي (USD)
- معالجة شاملة للأخطاء


"""

class ArabicNumberConverter:
    """
    فئة تحويل الأرقام إلى نص عربي فصيح
    """
    
    def __init__(self):
        """تهيئة المحول مع القوائم الأساسية للأرقام العربية"""
        
        # الآحاد (مذكر)
        self.ones_masculine = [
            "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", 
            "ستة", "سبعة", "ثمانية", "تسعة"
        ]
        
        # الآحاد (مؤنث) - للاستخدام مع المعدود المذكر
        self.ones_feminine = [
            "", "واحدة", "اثنتان", "ثلاث", "أربع", "خمس",
            "ست", "سبع", "ثمان", "تسع"
        ]
        
        # العشرات
        self.tens = [
            "", "", "عشرون", "ثلاثون", "أربعون", "خمسون",
            "ستون", "سبعون", "ثمانون", "تسعون"
        ]
        
        # المئات (مع التصحيح النحوي)
        self.hundreds = [
            "", "مئة", "مئتان", "ثلاثمئة", "أربعمئة", "خمسمئة",
            "ستمئة", "سبعمئة", "ثمانمئة", "تسعمئة"
        ]
        
        # الأرقام من 11-19
        self.teens = [
            "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", 
            "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"
        ]
        
        # أسماء العملات مع التصحيح النحوي الكامل
        self.currencies = {
        'USD': {
            'singular': 'دولار أمريكي',              # ١ دولار أمريكي
            'dual': 'دولاران أمريكيان',              # ٢ دولاران أمريكيان
            'plural_few': 'دولارات أمريكية',         # من ٣ إلى ١٠
            'plural_many': 'دولارًا أمريكيًا',       # من ١١ فما فوق
            'tamyeez': 'دولارًا أمريكيًا',           # تمييز منصوب في كل الحالات
            'symbol': '$',
            'masculine': True,
            'subunit': 'سنتًا',                       # مفرد منصوب
            'subunit_plural': 'سنتات'                 # جمع صحيح
        },
        'IQD': {
            'singular': 'دينار عراقي',               # ١ دينار عراقي
            'dual': 'ديناران عراقيان',               # ٢ ديناران عراقيان
            'plural_few': 'دنانير عراقية',           # من ٣ إلى ١٠
            'plural_many': 'دينارًا عراقيًا',         # من ١١ فما فوق
            'tamyeez': 'دينارًا عراقيًا',             # تمييز منصوب
            'symbol': '',
            'masculine': True,
            'subunit': 'فلسًا',                        # مفرد منصوب (في حال استخدام الفلس)
            'subunit_plural': 'فلوس'                  # جمع (اختياري للاستخدام حسب النظام)
        }
    }

        
    
    def _convert_group(self, num, is_currency_masculine=True):
        """
        تحويل مجموعة من ثلاثة أرقام إلى نص عربي
        
        Args:
            num (int): الرقم المراد تحويله (0-999)
            is_currency_masculine (bool): هل العملة مذكرة أم مؤنثة
            
        Returns:
            str: النص العربي للرقم
        """
        if num == 0:
            return ""
        
        result = ""
        
        # اختيار قائمة الآحاد المناسبة حسب جنس المعدود
        ones = self.ones_masculine if is_currency_masculine else self.ones_feminine
        
        # المئات
        if num >= 100:
            hundreds_digit = num // 100
            result += self.hundreds[hundreds_digit]
            num %= 100
            if num > 0:
                result += " و"
        
        # العشرات والآحاد
        if num >= 20:
            tens_digit = num // 10
            ones_digit = num % 10
            if ones_digit > 0:
                # ترتيب صحيح: الآحاد ثم العشرات (مثل: ثمانية وثمانون)
                result += ones[ones_digit] + " و" + self.tens[tens_digit]
            else:
                result += self.tens[tens_digit]
        elif num >= 10:
            result += self.teens[num - 10]
        elif num > 0:
            result += ones[num]
        
        return result
    
    def _get_currency_form(self, amount, currency_code, use_tamyeez=False):
        """
        تحديد الصيغة الصحيحة لاسم العملة حسب العدد مع دعم التمييز المنصوب

        Args:
            amount (int): المبلغ
            currency_code (str): رمز العملة (USD/IQD)
            use_tamyeez (bool): هل نستخدم التمييز المنصوب للأرقام الكبيرة

        Returns:
            str: الصيغة الصحيحة لاسم العملة
        """
        if currency_code not in self.currencies:
            return currency_code

        currency = self.currencies[currency_code]

        # استخدام التمييز المنصوب للأرقام الكبيرة (100+)
        if use_tamyeez and amount >= 100:
            return currency['tamyeez']

        # قواعد العدد والمعدود في العربية الفصحى
        if amount == 1:
            return currency['singular']
        elif amount == 2:
            return currency['dual']
        elif 3 <= amount <= 10:
            return currency['plural_few']
        elif 11 <= amount <= 99:  # الأرقام من 11 إلى 99
            return currency['plural_many']
        elif amount % 100 == 0:  # الأرقام المئوية (100، 200، 1000، إلخ)
            return currency['singular']
        elif amount % 100 == 1:  # الأرقام التي تنتهي بـ 1 (مثل 101، 1001)
            return currency['singular']
        elif amount % 100 == 2:  # الأرقام التي تنتهي بـ 2 (مثل 102، 1002)
            return currency['dual']
        elif 3 <= amount % 100 <= 10:  # الأرقام التي تنتهي بـ 3-10
            return currency['plural_few']
        elif 11 <= amount % 100 <= 99:  # الأرقام التي تنتهي بـ 11-99
            return currency['plural_many']
        else:  # باقي الأرقام
            return currency['singular']

    def _should_use_tamyeez(self, number):
        """
        تحديد متى نستخدم التمييز المنصوب وفق القواعد النحوية الصحيحة

        Args:
            number (int): الرقم المراد فحصه

        Returns:
            bool: هل نستخدم التمييز المنصوب أم لا
        """
        # التمييز المنصوب يُستخدم في الحالات التالية:
        # 1. الأرقام المركبة التي تحتوي على أجزاء من 11+ في الجزء الأخير
        # 2. الأرقام الكبيرة المركبة

        if number < 11:
            return False

        # المئات المفردة (100، 200، 300، إلخ) لا تحتاج تمييز منصوب
        if number % 100 == 0 and number < 1000:
            return False

        # الآلاف المفردة (1000، 2000، 3000، إلخ) لا تحتاج تمييز منصوب
        if number % 1000 == 0:
            return False

        # الملايين المفردة لا تحتاج تمييز منصوب
        if number % 1000000 == 0:
            return False

        # الأرقام من 11-99 تحتاج تمييز منصوب
        if 11 <= number <= 99:
            return True

        # الأرقام المركبة التي تنتهي بـ 11-99 تحتاج تمييز منصوب
        last_two_digits = number % 100
        if 11 <= last_two_digits <= 99:
            return True

        # الأرقام المركبة الأخرى (مثل 101، 102، 103) لا تحتاج تمييز منصوب
        return False

    def _convert_large_number(self, number, is_currency_masculine=True, has_millions=False):
        """
        تحويل الأرقام الكبيرة (آلاف، ملايين، مليارات)

        Args:
            number (int): الرقم المراد تحويله
            is_currency_masculine (bool): هل العملة مذكرة أم مؤنثة
            has_millions (bool): هل الرقم يحتوي على ملايين (يؤثر على تمييز الآلاف)

        Returns:
            str: النص العربي للرقم
        """
        if number == 0:
            return "صفر"

        if number < 1000:
            return self._convert_group(number, is_currency_masculine)

        result = ""
        original_number = number

        # المليارات
        if number >= 1000000000:
            billions = number // 1000000000
            if billions == 1:
                result += "مليار"
            elif billions == 2:
                result += "ملياران"
            elif billions <= 10:
                result += self._convert_group(billions, is_currency_masculine) + " مليارات"
            else:
                result += self._convert_group(billions, is_currency_masculine) + " مليار"

            number %= 1000000000
            if number > 0:
                result += " و"

        # الملايين
        if number >= 1000000:
            millions = number // 1000000
            if millions == 1:
                result += "مليون"
            elif millions == 2:
                result += "مليونان"
            elif millions <= 10:
                result += self._convert_group(millions, is_currency_masculine) + " ملايين"
            else:
                result += self._convert_group(millions, is_currency_masculine) + " مليون"

            number %= 1000000
            has_millions = True  # تم العثور على ملايين
            if number > 0:
                result += " و"

        # الآلاف (مع التمييز المنصوب الصحيح)
        if number >= 1000:
            thousands = number // 1000
            if thousands == 1:
                result += "ألف"
            elif thousands == 2:
                result += "ألفان"
            elif thousands <= 10:
                result += self._convert_group(thousands, is_currency_masculine) + " آلاف"
            else:
                # استخدام "ألفًا" فقط إذا لم تكن هناك ملايين
                if has_millions or original_number >= 1000000:
                    result += self._convert_group(thousands, is_currency_masculine) + " ألف"
                else:
                    result += self._convert_group(thousands, is_currency_masculine) + " ألفًا"

            number %= 1000
            if number > 0:
                result += " و"

        # المئات والعشرات والآحاد
        if number > 0:
            result += self._convert_group(number, is_currency_masculine)

        return result
    
    def convert_number_to_arabic_text(self, amount, currency='IQD'):
        """
        تحويل الرقم إلى نص عربي فصيح مع العملة
        
        Args:
            amount (float): المبلغ المراد تحويله
            currency (str): نوع العملة ('USD' أو 'IQD')
            
        Returns:
            str: النص العربي للمبلغ مع العملة
            
        Raises:
            ValueError: في حالة إدخال قيم غير صالحة
        """
        # التحقق من صحة الإدخال
        if not isinstance(amount, (int, float)) or amount < 0:
            raise ValueError("المبلغ يجب أن يكون رقمًا موجبًا")
        
        if currency not in self.currencies:
            raise ValueError(f"العملة غير مدعومة: {currency}. العملات المدعومة: {list(self.currencies.keys())}")
        
        # التعامل مع الصفر
        if amount == 0:
            return f"صفر {self.currencies[currency]['singular']}"
        
        # فصل الجزء الصحيح والعشري
        integer_part = int(amount)
        decimal_part = round((amount - integer_part) * 100)
        
        # تحويل الجزء الصحيح
        currency_info = self.currencies[currency]
        is_masculine = currency_info['masculine']
        
        text = self._convert_large_number(integer_part, is_masculine)
        
        # إضافة اسم العملة (مع التمييز المنصوب للأرقام المركبة فقط)
        use_tamyeez = self._should_use_tamyeez(integer_part)
        currency_name = self._get_currency_form(integer_part, currency, use_tamyeez)
        text += " " + currency_name
        
        # إضافة الجزء العشري إذا وُجد
        if decimal_part > 0:
            subunit_text = self._convert_large_number(decimal_part, is_masculine)
            if decimal_part == 1:
                text += " و" + subunit_text + " " + currency_info['subunit']
            else:
                text += " و" + subunit_text + " " + currency_info['subunit_plural']
        
        return text
    
    def format_number_with_currency(self, amount, currency='IQD'):
        """
        تنسيق الرقم بالفواصل والرمز النقدي
        
        Args:
            amount (float): المبلغ المراد تنسيقه
            currency (str): نوع العملة ('USD' أو 'IQD')
            
        Returns:
            str: الرقم المنسق مع رمز العملة
        """
        if not isinstance(amount, (int, float)) or amount < 0:
            return "0"
        
        if amount == 0:
            symbol = self.currencies.get(currency, {}).get('symbol', '')
            return f"0{symbol}" if symbol else "0"
        
        # تنسيق الرقم بالفواصل
        if isinstance(amount, float) and amount != int(amount):
            formatted = f"{amount:,.2f}"
        else:
            formatted = f"{int(amount):,}"
        
        # إضافة رمز العملة
        symbol = self.currencies.get(currency, {}).get('symbol', '')
        return f"{formatted}{symbol}" if symbol else formatted


# إنشاء مثيل عام للاستخدام
arabic_converter = ArabicNumberConverter()

# دوال مساعدة للاستخدام المباشر
def convert_number_to_arabic_text(amount, currency='IQD'):
    """دالة مساعدة لتحويل الرقم إلى نص عربي"""
    return arabic_converter.convert_number_to_arabic_text(amount, currency)

def format_number_with_currency(amount, currency='IQD'):
    """دالة مساعدة لتنسيق الرقم مع العملة"""
    return arabic_converter.format_number_with_currency(amount, currency)
