/**
 * Firebase Configuration for Client-Side - SECURE VERSION
 * إعدادات Firebase الآمنة للجانب الأمامي
 *
 * هذا الملف لا يحتوي على أي مفاتيح حساسة
 * جميع الإعدادات يتم تحميلها بشكل آمن من الخادم
 */

// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Firebase configuration will be loaded securely from server
let firebaseConfig = null;
let app = null;
let analytics = null;
let auth = null;
let db = null;
let storage = null;

// Secure Firebase initialization function
async function initializeFirebaseSecurely() {
    try {
        // Load Firebase config securely from server with authentication
        const response = await fetch('/api/secure-firebase-config', {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to load Firebase configuration');
        }

        firebaseConfig = await response.json();

        // Initialize Firebase with secure config
        app = initializeApp(firebaseConfig);
        analytics = getAnalytics(app);
        auth = getAuth(app);
        db = getFirestore(app);
        storage = getStorage(app);

        console.log('🔥 Firebase initialized securely');
        return { app, analytics, auth, db, storage };

    } catch (error) {
        console.error('❌ Failed to initialize Firebase:', error);
        throw error;
    }
}

// Export secure initialization function
export { initializeFirebaseSecurely };

// Export getters for Firebase services (will be null until initialized)
export const getFirebaseApp = () => app;
export const getFirebaseAuth = () => auth;
export const getFirebaseDb = () => db;
export const getFirebaseStorage = () => storage;
export const getFirebaseAnalytics = () => analytics;
