# QR Contract Viewer - Docker Image
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV FLASK_ENV=production

# إنشاء مستخدم غير root للأمان
RUN groupadd -r appuser && useradd -r -g appuser appuser

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات وتثبيتها
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# تغيير ملكية الملفات للمستخدم الجديد
RUN chown -R appuser:appuser /app

# التبديل للمستخدم غير root
USER appuser

# كشف المنفذ
EXPOSE 21226

# فحص صحة الحاوية
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:21226/health || exit 1

# تشغيل التطبيق
CMD ["gunicorn", "wsgi:application", "--bind", "0.0.0.0:21226", "--workers", "4", "--timeout", "120"]
