#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Contract Viewer Server - SECURE VERSION
==========================================

سيرفر Flask آمن لعرض العقود مع حماية متقدمة
يوفر أعلى معايير الأمان والحماية من الثغرات

الميزات الأمنية:
- تشفير AES-256 للبيانات الحساسة
- مصادقة JWT مع حماية CSRF
- حماية من هجمات الحقن والـ XSS
- Rate limiting متقدم
- مراقبة الأنشطة المشبوهة
- HTTPS إجباري
"""

import os
import logging
import secrets
from flask import Flask, render_template, abort, request, jsonify, session, redirect, url_for
import json
from datetime import datetime, timedelta
import time
from functools import wraps
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# Import security modules
from security import (
    encryption_manager, encrypt_sensitive_data, decrypt_sensitive_data,
    auth_manager, require_auth, require_csrf, require_permission,
    api_protection, rate_limit, validate_json_input,
    sanitize_form_input, block_suspicious_requests,
    security_monitor, log_security_event, detect_attack_patterns,
    analyze_request_anomaly, is_ip_blocked, get_security_stats
)
from security.https_enforcer import https_enforcer, require_https

# متغير حالة Firebase
FIREBASE_ENABLED = False

def init_firebase():
    """تهيئة Firebase Realtime Database"""
    global FIREBASE_ENABLED
    try:
        import firebase_admin
        from firebase_admin import credentials, db

        # التحقق من وجود تطبيق Firebase مُهيأ مسبقاً
        if firebase_admin._apps:
            FIREBASE_ENABLED = True
            logger.info("✅ Firebase مُهيأ مسبقاً")
            return

        # إنشاء credentials من متغيرات البيئة
        cred_dict = {
            "type": os.getenv('FIREBASE_TYPE'),
            "project_id": os.getenv('FIREBASE_PROJECT_ID'),
            "private_key_id": os.getenv('FIREBASE_PRIVATE_KEY_ID'),
            "private_key": os.getenv('FIREBASE_PRIVATE_KEY').replace('\\n', '\n'),
            "client_email": os.getenv('FIREBASE_CLIENT_EMAIL'),
            "client_id": os.getenv('FIREBASE_CLIENT_ID'),
            "auth_uri": os.getenv('FIREBASE_AUTH_URI'),
            "token_uri": os.getenv('FIREBASE_TOKEN_URI'),
            "auth_provider_x509_cert_url": os.getenv('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
            "client_x509_cert_url": os.getenv('FIREBASE_CLIENT_X509_CERT_URL'),
            "universe_domain": os.getenv('FIREBASE_UNIVERSE_DOMAIN', 'googleapis.com')
        }

        # تهيئة Firebase Admin
        cred = credentials.Certificate(cred_dict)
        firebase_admin.initialize_app(cred, {
            'databaseURL': 'https://car-report-a01fe-default-rtdb.firebaseio.com'
        })

        FIREBASE_ENABLED = True
        logger.info("[SUCCESS] تم تهيئة Firebase Realtime Database بنجاح")

    except Exception as e:
        FIREBASE_ENABLED = False
        logger.error(f"[ERROR] خطأ في تهيئة Firebase: {e}")
        logger.info("سيتم استخدام الملف المحلي كبديل")

# إعداد التطبيق مع أعلى معايير الأمان
app = Flask(__name__)

# إعدادات الأمان المتقدمة
app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', secrets.token_urlsafe(64))
app.config['SESSION_COOKIE_SECURE'] = True  # HTTPS إجباري
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Strict'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=1)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload

# إعدادات أمان إضافية
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 300  # 5 minutes cache
app.config['JSONIFY_PRETTYPRINT_REGULAR'] = False  # تقليل حجم الاستجابة

# إعداد التسجيل المتقدم مع دعم Unicode
import sys
import codecs

# تعيين encoding للـ console في Windows قبل إعداد logging
if sys.platform.startswith('win'):
    try:
        # تعيين encoding للـ console
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
    except:
        pass

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('security.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Security Headers Middleware
@app.after_request
def add_security_headers(response):
    """إضافة headers أمنية متقدمة"""
    # HTTPS Enforcement
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'

    # XSS Protection
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'

    # Content Security Policy
    csp = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' https://www.gstatic.com https://www.googleapis.com; "
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
        "font-src 'self' https://fonts.gstatic.com; "
        "img-src 'self' data: https:; "
        "connect-src 'self' https://identitytoolkit.googleapis.com https://*.firebaseio.com; "
        "frame-ancestors 'none'; "
        "base-uri 'self'; "
        "form-action 'self'"
    )
    response.headers['Content-Security-Policy'] = csp

    # Additional Security Headers
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

# تهيئة Firebase
init_firebase()

# تهيئة HTTPS enforcer
https_enforcer.init_app(app)

# Middleware للتحقق من IP المحظورة - معطل
# @app.before_request
# def check_blocked_ip():
#     """فحص IP المحظورة قبل معالجة الطلب - معطل"""
#     pass

# Middleware لتحليل الطلبات المشبوهة - معطل
# @app.before_request
# def analyze_request_security():
#     """تحليل أمان الطلب قبل المعالجة - معطل"""
#     pass

# مسار ملف البيانات
DATA_FILE = os.path.join(os.path.dirname(__file__), '..', 'contracts_data.json')

def load_contracts_data():
    """تحميل بيانات العقود من الملف المحلي"""
    try:
        if os.path.exists(DATA_FILE):
            with open(DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.warning(f"ملف البيانات غير موجود: {DATA_FILE}")
            return {}
    except Exception as e:
        logger.error(f"خطأ في تحميل البيانات: {e}")
        return {}

# حماية من الهجمات - تم نقلها إلى security module
# Rate limiting is now handled by security.api_protection module

def validate_uuid(uuid_string):
    """التحقق من صحة UUID"""
    import re
    uuid_pattern = re.compile(
        r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
        re.IGNORECASE
    )
    return bool(uuid_pattern.match(uuid_string))

def normalize_contract_data(contract_data, contract_uuid):
    """تطبيع بيانات العقد وإضافة قيم افتراضية"""
    try:
        normalized = {
            'uuid': contract_uuid,
            'serial_number': contract_data.get('serial_number', 'غير محدد'),
            'date': contract_data.get('date', 'غير محدد'),
            'time': contract_data.get('time', contract_data.get('t', 'غير محدد')),
            'period': contract_data.get('period', contract_data.get('t_1', '')),
            'day': contract_data.get('day', ''),

            # بيانات المالك الشرعي
            'legal_owner_name': contract_data.get('legal_owner_name', contract_data.get('name_1', '')),
            'legal_owner_address': contract_data.get('legal_owner_address', contract_data.get('location_1', '')),

            # بيانات البائع
            'seller_name': contract_data.get('seller_name', contract_data.get('name_2', 'غير محدد')),
            'seller_address': contract_data.get('seller_address', contract_data.get('location_2', '')),
            'seller_id': contract_data.get('seller_id', contract_data.get('id_1', 'غير محدد')),
            'seller_phone': contract_data.get('seller_phone', contract_data.get('phone_1', '')),

            # بيانات المشتري
            'buyer_name': contract_data.get('buyer_name', contract_data.get('name_3', 'غير محدد')),
            'buyer_address': contract_data.get('buyer_address', contract_data.get('location_3', '')),
            'buyer_id': contract_data.get('buyer_id', contract_data.get('id_2', 'غير محدد')),
            'buyer_phone': contract_data.get('buyer_phone', contract_data.get('phone_2', '')),

            # بيانات السيارة
            'car_make': contract_data.get('car_make', contract_data.get('car_type', '')),
            'car_model': contract_data.get('car_model', ''),
            'car_year': contract_data.get('car_year', ''),
            'car_color': contract_data.get('car_color', contract_data.get('car_colar', '')),
            'car_number': contract_data.get('car_number', contract_data.get('car_num', '')),
            'car_city': contract_data.get('car_city', ''),
            'car_property': contract_data.get('car_property', contract_data.get('car_prop', '')),
            'annual_number': contract_data.get('annual_number', contract_data.get('sin_num', '')),
            'chassis_number': contract_data.get('chassis_number', contract_data.get('sasi_num', '')),

            # البيانات المالية
            'sale_amount': contract_data.get('sale_amount', contract_data.get('badal_num', '0')),
            'paid_amount': contract_data.get('paid_amount', contract_data.get('mony_num', '')),
            'remaining_amount': contract_data.get('remaining_amount', contract_data.get('mony_not_delevired', '')),
            'currency': contract_data.get('currency', contract_data.get('currency_type', 'IQD')),
            'amount_in_words': contract_data.get('amount_in_words', ''),

            # الملاحظات
            'note_a': contract_data.get('note_a', ''),
            'note_b': contract_data.get('note_b', ''),
            'm1': contract_data.get('m1', ''),  # ملاحظات الباقي

            # معلومات إضافية
            'created_at': contract_data.get('created_at', 'غير محدد'),
            'computer_name': contract_data.get('computer_name', 'غير محدد'),

            # سجل التعديلات
            'modifications_history': contract_data.get('modifications_history', []),

            # الحقول القديمة للتوافق مع النماذج القديمة
            'name_1': contract_data.get('name_1', ''),
            'name_2': contract_data.get('name_2', ''),
            'name_3': contract_data.get('name_3', ''),
            'location_1': contract_data.get('location_1', ''),
            'location_2': contract_data.get('location_2', ''),
            'location_3': contract_data.get('location_3', ''),
            'id_1': contract_data.get('id_1', ''),
            'id_2': contract_data.get('id_2', ''),
            'phone_1': contract_data.get('phone_1', ''),
            'phone_2': contract_data.get('phone_2', ''),
            'car_type': contract_data.get('car_type', ''),
            'car_colar': contract_data.get('car_colar', ''),
            'car_num': contract_data.get('car_num', ''),
            'car_prop': contract_data.get('car_prop', ''),
            'sin_num': contract_data.get('sin_num', ''),
            'sasi_num': contract_data.get('sasi_num', ''),
            'badal_num': contract_data.get('badal_num', ''),
            'mony_num': contract_data.get('mony_num', ''),
            'mony_not_delevired': contract_data.get('mony_not_delevired', ''),
            'currency_type': contract_data.get('currency_type', ''),
            't': contract_data.get('t', ''),
            't_1': contract_data.get('t_1', '')
        }

        # تنظيف المبالغ من رموز العملة والفواصل
        def clean_amount(amount_value):
            """تنظيف المبلغ من رموز العملة والفواصل"""
            if amount_value is None:
                return '0'

            # تحويل إلى string
            amount_str = str(amount_value).strip()

            if amount_str == '' or amount_str.lower() == 'none':
                return '0'

            # إزالة رموز العملة والفواصل والمسافات
            cleaned = amount_str.replace('$', '').replace('دينار', '').replace(',', '').replace(' ', '').strip()

            # إزالة أي نصوص إضافية
            import re
            # استخراج الأرقام فقط (مع النقطة العشرية)
            numbers_only = re.findall(r'\d+\.?\d*', cleaned)

            if numbers_only:
                try:
                    # أخذ أول رقم موجود
                    clean_number = numbers_only[0]
                    # التحقق من صحة الرقم
                    float(clean_number)
                    return clean_number
                except (ValueError, TypeError):
                    return '0'
            else:
                return '0'

        # تطبيق التنظيف على جميع المبالغ
        normalized['sale_amount'] = clean_amount(normalized['sale_amount'])
        normalized['paid_amount'] = clean_amount(normalized['paid_amount'])
        normalized['remaining_amount'] = clean_amount(normalized['remaining_amount'])

        return normalized

    except Exception as e:
        logger.error(f"Error normalizing contract data: {e}")
        # إرجاع بيانات أساسية في حالة الخطأ
        return {
            'uuid': contract_uuid,
            'serial_number': 'غير محدد',
            'date': 'غير محدد',
            'time': 'غير محدد',
            'seller_name': 'غير محدد',
            'buyer_name': 'غير محدد',
            'sale_amount': 0,
            'currency': 'IQD'
        }

def get_contract_from_firebase(contract_uuid):
    """جلب العقد من Firebase Realtime Database"""
    try:
        logger.info(f"Searching for contract: {contract_uuid}")

        if not FIREBASE_ENABLED:
            logger.warning("Firebase not enabled")
            return None

        # استخدام Firebase Realtime Database بدلاً من Firestore
        import firebase_admin
        from firebase_admin import db

        # التحقق من تهيئة Firebase
        if not firebase_admin._apps:
            logger.error("Firebase not initialized")
            return None

        # البحث في finalized_contracts
        ref = db.reference('finalized_contracts')
        all_contracts = ref.get()

        if not all_contracts:
            logger.info(f"No contracts found in Firebase")
            return None

        logger.info(f"Found {len(all_contracts)} contracts in Firebase")

        # البحث عن العقد بـ UUID
        for contract_id, contract_data in all_contracts.items():
            if contract_data and isinstance(contract_data, dict):
                try:
                    # البحث في contract_data إذا كان العقد محفوظ بهذا التنسيق
                    if contract_data.get('uuid') == contract_uuid:
                        logger.info(f"Contract found in Firebase: {contract_uuid}")
                        # إرجاع بيانات العقد الفعلية
                        if 'contract_data' in contract_data:
                            contract_info = contract_data['contract_data']
                            # إضافة سجل التعديلات إلى بيانات العقد
                            contract_info['modifications_history'] = contract_data.get('modifications_history', [])
                            # إضافة قيم افتراضية للحقول المفقودة
                            return normalize_contract_data(contract_info, contract_uuid)
                        else:
                            # إضافة سجل التعديلات إلى بيانات العقد
                            contract_data['modifications_history'] = contract_data.get('modifications_history', [])
                            return normalize_contract_data(contract_data, contract_uuid)

                    # البحث في contract_data المدمجة
                    nested_contract = contract_data.get('contract_data', {})
                    if nested_contract and isinstance(nested_contract, dict) and nested_contract.get('uuid') == contract_uuid:
                        logger.info(f"Contract found in nested data: {contract_uuid}")
                        # إضافة سجل التعديلات إلى بيانات العقد
                        nested_contract['modifications_history'] = contract_data.get('modifications_history', [])
                        return normalize_contract_data(nested_contract, contract_uuid)

                    # إذا لم يوجد UUID في contract_data، نحاول إنشاء بيانات العقد من البيانات الموجودة
                    if nested_contract and isinstance(nested_contract, dict):
                        # إضافة UUID إذا لم يكن موجود
                        if not nested_contract.get('uuid') and contract_data.get('uuid') == contract_uuid:
                            logger.info(f"Contract found with UUID in parent: {contract_uuid}")
                            # إضافة سجل التعديلات إلى بيانات العقد
                            nested_contract['modifications_history'] = contract_data.get('modifications_history', [])
                            return normalize_contract_data(nested_contract, contract_uuid)

                except Exception as e:
                    logger.warning(f"Error processing contract {contract_id}: {e}")
                    continue

        logger.info(f"Contract not found in Firebase: {contract_uuid}")
        return None

    except Exception as e:
        logger.error(f"خطأ في جلب العقد من Firebase: {e}")
        return None

def get_contract_from_data(contract_uuid):
    """جلب العقد من الملف المحلي"""
    try:
        contracts_data = load_contracts_data()

        if not contracts_data:
            return None

        # البحث في العقود المكتملة
        finalized_contracts = contracts_data.get('finalized_contracts', {})

        # البحث في جميع السنوات
        for year_contracts in finalized_contracts.values():
            if isinstance(year_contracts, dict):
                for contract_data in year_contracts.values():
                    if contract_data and contract_data.get('uuid') == contract_uuid:
                        return contract_data

        return None
    except Exception as e:
        logger.error(f"خطأ في جلب العقد: {e}")
        return None

def get_contract(contract_uuid):
    """جلب العقد من Firebase أولاً، ثم من الملف المحلي كبديل"""
    try:
        # محاولة جلب العقد من Firebase أولاً
        if FIREBASE_ENABLED:
            contract_data = get_contract_from_firebase(contract_uuid)
            if contract_data:
                return contract_data
            logger.info(f"Contract not found in Firebase, trying local file: {contract_uuid}")

        # إذا لم يوجد في Firebase أو Firebase غير متاح، جرب الملف المحلي
        contract_data = get_contract_from_data(contract_uuid)
        if contract_data:
            logger.info(f"Contract found in local file: {contract_uuid}")
            return contract_data

        logger.info(f"Contract not found anywhere: {contract_uuid}")
        return None

    except Exception as e:
        logger.error(f"خطأ في جلب العقد: {e}")
        return None

@app.route('/')
def index():
    """الصفحة الرئيسية الآمنة"""
    try:
        # إنشاء CSRF token للصفحة
        csrf_token = auth_manager.generate_csrf_token()

        # تسجيل الوصول
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        logger.info(f"📄 Index page accessed from IP: {client_ip}")

        return render_template('index.html', csrf_token=csrf_token)

    except Exception as e:
        logger.error(f"❌ Error loading index page: {e}")
        return render_template('500.html'), 500

@app.route('/contract/<contract_uuid>')
def view_contract(contract_uuid):
    """عرض العقد مع حماية متقدمة"""
    try:
        # التحقق من صحة UUID
        if not validate_uuid(contract_uuid):
            logger.warning(f"Invalid UUID: {contract_uuid}")
            abort(404)

        # جلب العقد من Firebase أو الملف المحلي
        contract_data = get_contract(contract_uuid)

        if not contract_data:
            logger.info(f"📄 Contract not found: {contract_uuid}")
            abort(404)

        # التحقق من أن البيانات صحيحة
        if not isinstance(contract_data, dict) or not contract_data.get('uuid'):
            logger.error(f"❌ Invalid contract data: {contract_uuid}")
            abort(404)

        # تسجيل الوصول للعقد
        logger.info(f"Contract accessed: {contract_uuid}")

        return render_template('contract.html', contract=contract_data)

    except Exception as e:
        logger.error(f"❌ Error viewing contract {contract_uuid}: {e}")

        # تسجيل الخطأ
        logger.error(f"Contract access error: {str(e)}")

        # إذا كان الخطأ متعلق بعدم وجود العقد، أرجع 404
        if "not found" in str(e).lower() or "غير موجود" in str(e):
            abort(404)
        else:
            abort(500)

# Security headers already defined above - removing duplicate

@app.route('/api/verify-token', methods=['POST'])
@rate_limit(limit=100, window=3600)  # Default rate limit
def verify_firebase_token():
    """التحقق من رمز Firebase"""
    try:
        if not FIREBASE_ENABLED:
            return jsonify({"error": "Firebase not enabled"}), 503

        data = request.get_json()
        if not data or 'idToken' not in data:
            return jsonify({"error": "Missing idToken"}), 400

        # للآن، سنقبل أي رمز (يمكن تحسين هذا لاحقاً)
        return jsonify({"status": "authenticated", "message": "Token accepted"})

    except Exception as e:
        logger.error(f"Error verifying token: {e}")
        return jsonify({"error": "Authentication failed"}), 401

@app.route('/api/secure-firebase-config')
@require_auth
@require_csrf
@rate_limit(limit=10, window=3600)  # 10 requests per hour
@validate_json_input()
@block_suspicious_requests
def get_secure_firebase_config():
    """إرجاع إعدادات Firebase الآمنة للجانب الأمامي (بعد المصادقة فقط)"""
    try:
        # التحقق من صلاحيات المستخدم
        user = getattr(request, 'user', None)
        if not user:
            api_protection.log_security_event('unauthorized_firebase_config_access', {
                'ip': request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            })
            return jsonify({'error': 'Unauthorized access'}), 401

        # إعدادات Firebase العامة فقط (بدون مفاتيح حساسة)
        config = {
            "authDomain": "car-report-a01fe.firebaseapp.com",
            "projectId": "car-report-a01fe",
            "storageBucket": "car-report-a01fe.firebasestorage.app",
            "messagingSenderId": "356521538113",
            "appId": "1:356521538113:web:8c74579cbc72e05e334cc0"
        }

        # تسجيل الوصول الآمن
        logger.info(f"🔐 Secure Firebase config accessed by user: {user.get('user_id')}")

        return jsonify(config)

    except Exception as e:
        logger.error(f"❌ Error in secure Firebase config: {e}")
        return jsonify({'error': 'Configuration access failed'}), 500

# ===== Secure Authentication Endpoints =====

@app.route('/api/auth/login', methods=['POST'])
@rate_limit(limit=5, window=900)  # 5 attempts per 15 minutes
@validate_json_input(max_length=1000)
@sanitize_form_input
@block_suspicious_requests
def secure_login():
    """تسجيل دخول آمن مع حماية متقدمة"""
    try:
        client_id = auth_manager.get_client_identifier()

        # فحص Rate Limiting
        if not auth_manager.check_rate_limit(client_id):
            api_protection.log_security_event('login_rate_limit_exceeded', {
                'client_id': client_id
            })
            return jsonify({'error': 'Too many login attempts. Try again later.'}), 429

        # الحصول على بيانات تسجيل الدخول
        data = request.get_json() if request.is_json else request.form
        username = data.get('username', '').strip()
        password = data.get('password', '')

        if not username or not password:
            auth_manager.record_failed_attempt(client_id)
            return jsonify({'error': 'Username and password required'}), 400

        # التحقق من بيانات الاعتماد (هنا يمكن ربطها بقاعدة البيانات)
        # للتبسيط، سنستخدم مستخدم افتراضي
        if username == 'admin' and password == 'admin123':  # يجب تغيير هذا في الإنتاج
            # مسح محاولات الفشل
            auth_manager.clear_failed_attempts(client_id)

            # إنشاء JWT token
            permissions = ['admin', 'read', 'write']
            jwt_token = auth_manager.generate_jwt_token(username, permissions)

            # إنشاء CSRF token
            csrf_token = auth_manager.generate_csrf_token()

            # تسجيل تسجيل الدخول الناجح
            logger.info(f"✅ Successful login for user: {username}")

            return jsonify({
                'success': True,
                'token': jwt_token,
                'csrf_token': csrf_token,
                'user': {
                    'username': username,
                    'permissions': permissions
                }
            })
        else:
            # تسجيل محاولة فاشلة
            auth_manager.record_failed_attempt(client_id)
            api_protection.log_security_event('failed_login_attempt', {
                'username': username,
                'client_id': client_id
            })
            return jsonify({'error': 'Invalid credentials'}), 401

    except Exception as e:
        logger.error(f"❌ Login error: {e}")
        return jsonify({'error': 'Login failed'}), 500

@app.route('/api/auth/logout', methods=['POST'])
@require_auth
@require_csrf
def secure_logout():
    """تسجيل خروج آمن"""
    try:
        user = getattr(request, 'user', None)
        if user:
            logger.info(f"🚪 User logged out: {user.get('user_id')}")

        # مسح session
        session.clear()

        return jsonify({'success': True, 'message': 'Logged out successfully'})

    except Exception as e:
        logger.error(f"❌ Logout error: {e}")
        return jsonify({'error': 'Logout failed'}), 500

@app.route('/api/auth/verify', methods=['GET'])
@require_auth
def verify_token():
    """التحقق من صحة الـ token"""
    try:
        user = getattr(request, 'user', None)
        if user:
            return jsonify({
                'valid': True,
                'user': {
                    'user_id': user.get('user_id'),
                    'permissions': user.get('permissions', [])
                }
            })
        else:
            return jsonify({'valid': False}), 401

    except Exception as e:
        logger.error(f"❌ Token verification error: {e}")
        return jsonify({'valid': False}), 401

@app.route('/api/csrf-token', methods=['GET'])
def get_csrf_token():
    """الحصول على CSRF token"""
    try:
        csrf_token = auth_manager.generate_csrf_token()
        return jsonify({'csrf_token': csrf_token})

    except Exception as e:
        logger.error(f"❌ CSRF token generation error: {e}")
        return jsonify({'error': 'Failed to generate CSRF token'}), 500

# ===== Security Monitoring Endpoints =====

@app.route('/api/security/stats')
@require_auth
@require_permission('admin')
@rate_limit(limit=10, window=3600)
def get_security_statistics():
    """الحصول على إحصائيات الأمان (للمدراء فقط)"""
    try:
        stats = get_security_stats()

        # إضافة معلومات إضافية
        stats['server_info'] = {
            'https_enforced': https_enforcer.https_only,
            'security_monitoring_active': security_monitor.monitoring_active,
            'encryption_enabled': True
        }

        return jsonify(stats)

    except Exception as e:
        logger.error(f"❌ Error getting security stats: {e}")
        return jsonify({'error': 'Failed to get security statistics'}), 500

@app.route('/api/security/block-ip', methods=['POST'])
@require_auth
@require_permission('admin')
@require_csrf
@rate_limit(limit=5, window=3600)
@validate_json_input(max_length=500)
def manual_block_ip():
    """حظر IP يدوياً (للمدراء فقط)"""
    try:
        data = request.get_json()
        ip_to_block = data.get('ip', '').strip()
        reason = data.get('reason', 'manual_admin_block').strip()

        if not ip_to_block:
            return jsonify({'error': 'IP address required'}), 400

        # التحقق من صحة IP
        import ipaddress
        try:
            ipaddress.ip_address(ip_to_block)
        except ValueError:
            return jsonify({'error': 'Invalid IP address format'}), 400

        # حظر IP
        security_monitor.block_ip(ip_to_block, reason)

        user = getattr(request, 'user', {})
        log_security_event('manual_ip_block', {
            'blocked_ip': ip_to_block,
            'reason': reason,
            'admin_user': user.get('user_id', 'unknown')
        }, 'high')

        return jsonify({
            'success': True,
            'message': f'IP {ip_to_block} has been blocked',
            'reason': reason
        })

    except Exception as e:
        logger.error(f"❌ Error blocking IP: {e}")
        return jsonify({'error': 'Failed to block IP'}), 500

@app.route('/api/security/unblock-ip', methods=['POST'])
@require_auth
@require_permission('admin')
@require_csrf
@rate_limit(limit=5, window=3600)
@validate_json_input(max_length=500)
def manual_unblock_ip():
    """إلغاء حظر IP يدوياً (للمدراء فقط)"""
    try:
        data = request.get_json()
        ip_to_unblock = data.get('ip', '').strip()

        if not ip_to_unblock:
            return jsonify({'error': 'IP address required'}), 400

        # إلغاء حظر IP
        if ip_to_unblock in security_monitor.blocked_ips:
            security_monitor.blocked_ips.remove(ip_to_unblock)

            user = getattr(request, 'user', {})
            log_security_event('manual_ip_unblock', {
                'unblocked_ip': ip_to_unblock,
                'admin_user': user.get('user_id', 'unknown')
            }, 'medium')

            return jsonify({
                'success': True,
                'message': f'IP {ip_to_unblock} has been unblocked'
            })
        else:
            return jsonify({'error': 'IP is not currently blocked'}), 400

    except Exception as e:
        logger.error(f"❌ Error unblocking IP: {e}")
        return jsonify({'error': 'Failed to unblock IP'}), 500

@app.route('/health')
def health_check():
    """فحص صحة السيرفر"""
    status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "firebase_enabled": FIREBASE_ENABLED
    }

    if FIREBASE_ENABLED:
        try:
            # فحص اتصال Firebase Realtime Database
            import firebase_admin
            from firebase_admin import db

            if firebase_admin._apps:
                ref = db.reference('/')
                # محاولة قراءة بسيطة للتأكد من الاتصال
                ref.get()
                status["firebase_status"] = "connected"
            else:
                status["firebase_status"] = "not_initialized"
        except Exception as e:
            status["firebase_status"] = f"error: {str(e)}"

    return jsonify(status)

@app.errorhandler(404)
def not_found(error):
    """صفحة العقد غير موجود"""
    logger.warning(f"صفحة غير موجودة: {request.url}")
    return render_template('404.html'), 404

@app.errorhandler(429)
def rate_limit_exceeded(error):
    """تجاوز الحد الأقصى للطلبات"""
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
    logger.warning(f"تجاوز الحد الأقصى للطلبات من IP: {client_ip}")
    return jsonify({"error": "تم تجاوز الحد الأقصى للطلبات"}), 429

@app.errorhandler(500)
def internal_error(error):
    """خطأ داخلي"""
    logger.error(f"خطأ داخلي: {error}")
    return render_template('500.html'), 500

if __name__ == '__main__':
    # تشغيل السيرفر
    port = int(os.environ.get('PORT', 21226))
    debug = os.environ.get('FLASK_ENV', 'production') == 'development'

    logger.info(f"بدء تشغيل سيرفر QR على المنفذ {port}")
    logger.info(f"مسار ملف البيانات: {DATA_FILE}")

    app.run(host='0.0.0.0', port=port, debug=debug)
