# مدير Firebase للتفاعل مع قاعدة البيانات
import firebase_admin
from firebase_admin import credentials, db
from firebase_config import get_firebase_config, DATABASE_URL, FIREBASE_PATHS
import json
from datetime import datetime

class FirebaseManager:
    def __init__(self):
        self.app = None
        self.database = None
        self._connection_pool = {}
        self._last_activity = {}
        self._max_idle_time = 300  # 5 دقائق
        self.initialize_firebase()

    def initialize_firebase(self):
        """تهيئة Firebase Admin SDK مع تحسينات الأداء"""
        try:
            # التحقق من وجود تطبيق Firebase مُهيأ مسبقاً
            if not firebase_admin._apps:
                # قراءة إعدادات Service Account
                service_account = get_firebase_config()

                # إنشاء credentials من البيانات
                cred = credentials.Certificate(service_account)

                # تهيئة التطبيق مع إعدادات محسنة للأداء
                self.app = firebase_admin.initialize_app(cred, {
                    'databaseURL': DATABASE_URL,
                    'httpTimeout': 30,  # مهلة انتظار أقصر
                })
                print("✅ تم تهيئة Firebase بنجاح مع تحسينات الأداء")
            else:
                self.app = firebase_admin.get_app()
                print("✅ Firebase مُهيأ مسبقاً")

            # الحصول على مرجع قاعدة البيانات
            self.database = db.reference()

        except Exception as e:
            print(f"❌ خطأ في تهيئة Firebase: {e}")
            raise e

    def _get_cached_reference(self, path):
        """الحصول على مرجع مخزن مؤقتاً لتحسين الأداء"""
        current_time = datetime.now().timestamp()

        # تنظيف المراجع القديمة
        expired_paths = []
        for cached_path, last_used in self._last_activity.items():
            if current_time - last_used > self._max_idle_time:
                expired_paths.append(cached_path)

        for expired_path in expired_paths:
            if expired_path in self._connection_pool:
                del self._connection_pool[expired_path]
            if expired_path in self._last_activity:
                del self._last_activity[expired_path]

        # إنشاء أو استخدام مرجع مخزن
        if path not in self._connection_pool:
            self._connection_pool[path] = self.database.child(path)

        self._last_activity[path] = current_time
        return self._connection_pool[path]
    
    def get_data(self, path):
        """قراءة البيانات من Firebase مع تحسينات الأداء"""
        try:
            ref = self._get_cached_reference(path)
            data = ref.get()
            return data if data is not None else {}
        except Exception as e:
            print(f"❌ خطأ في قراءة البيانات من {path}: {e}")
            # إعادة المحاولة مرة واحدة في حالة فشل الاتصال
            try:
                import time
                time.sleep(1)  # انتظار قصير
                ref = self.database.child(path)  # استخدام مرجع جديد
                data = ref.get()
                return data if data is not None else {}
            except:
                return {}

    def set_data(self, path, data):
        """كتابة البيانات إلى Firebase مع تحسينات الأداء"""
        try:
            ref = self._get_cached_reference(path)
            ref.set(data)
            return True
        except Exception as e:
            print(f"❌ خطأ في كتابة البيانات إلى {path}: {e}")
            # إعادة المحاولة مرة واحدة
            try:
                import time
                time.sleep(1)
                ref = self.database.child(path)
                ref.set(data)
                return True
            except:
                return False

    def update_data(self, path, data):
        """تحديث البيانات في Firebase مع تحسينات الأداء"""
        try:
            ref = self._get_cached_reference(path)
            ref.update(data)
            return True
        except Exception as e:
            print(f"❌ خطأ في تحديث البيانات في {path}: {e}")
            return False

    def delete_data(self, path):
        """حذف البيانات من Firebase مع تحسينات الأداء"""
        try:
            ref = self._get_cached_reference(path)
            ref.delete()
            return True
        except Exception as e:
            print(f"❌ خطأ في حذف البيانات من {path}: {e}")
            return False

    def push_data(self, path, data):
        """إضافة بيانات جديدة مع مفتاح تلقائي"""
        try:
            ref = self._get_cached_reference(path)
            new_ref = ref.push(data)
            return new_ref.key
        except Exception as e:
            print(f"❌ خطأ في إضافة البيانات إلى {path}: {e}")
            return None

# إنشاء مثيل واحد من FirebaseManager للاستخدام في جميع أنحاء التطبيق
firebase_manager = FirebaseManager()

# ===== دوال إدارة المستخدمين =====
def load_computers():
    """قراءة بيانات المستخدمين من Firebase"""
    return firebase_manager.get_data(FIREBASE_PATHS['computers'])

def save_computers(computers):
    """حفظ بيانات المستخدمين في Firebase"""
    return firebase_manager.set_data(FIREBASE_PATHS['computers'], computers)

# ===== دوال إدارة الرقم التسلسلي =====
def load_serial_number():
    """قراءة الرقم التسلسلي من Firebase"""
    data = firebase_manager.get_data(FIREBASE_PATHS['serial_number'])
    if isinstance(data, dict):
        return data.get('current_number', 0)
    return 0

def save_serial_number(number):
    """حفظ الرقم التسلسلي في Firebase"""
    data = {
        'current_number': number,
        'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    return firebase_manager.set_data(FIREBASE_PATHS['serial_number'], data)

# ===== دوال إدارة إعدادات الإدارة =====
def load_admin_config():
    """قراءة إعدادات الإدارة من Firebase"""
    data = firebase_manager.get_data(FIREBASE_PATHS['admin_config'])
    if not data:
        # إنشاء إعدادات افتراضية
        default_config = {'password': 'admin1'}
        save_admin_config(default_config)
        return default_config
    return data

def save_admin_config(config):
    """حفظ إعدادات الإدارة في Firebase"""
    return firebase_manager.set_data(FIREBASE_PATHS['admin_config'], config)

# ===== دوال إدارة العقود المحفوظة =====
def save_contract_data(computer_name, contract_data, is_editing=False, images=None):
    """حفظ بيانات العقد في Firebase"""
    contract_info = {
        'contract_info': {
            'serial_number': contract_data.get('serial_number', ''),
            'created_at': contract_data.get('created_at', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
            'last_modified': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'computer_name': computer_name,
            'is_editing': is_editing
        },
        'form_data': {k: v for k, v in contract_data.items() if k not in ['serial_number', 'created_at']},
        'images': images or {}
    }
    
    path = f"{FIREBASE_PATHS['saved_contracts']}/{computer_name}"
    success = firebase_manager.set_data(path, contract_info)
    
    if success:
        # تحديث حالة وجود عقد محفوظ في بيانات المستخدمين
        computers = load_computers()
        if computer_name and computer_name in computers:
            computers[computer_name]['has_saved_contract'] = True
            save_computers(computers)
    
    return success

def load_saved_contract(computer_id_or_name):
    """قراءة العقد المحفوظ من Firebase"""
    # محاولة البحث بالاسم أولاً
    if isinstance(computer_id_or_name, str) and not computer_id_or_name.isdigit():
        path = f"{FIREBASE_PATHS['saved_contracts']}/{computer_id_or_name}"
        data = firebase_manager.get_data(path)
        if data:
            return data

    # البحث بـ computer_id (للتوافق مع الكود القديم)
    # البحث في جميع العقود المحفوظة للعثور على العقد بـ computer_id
    all_contracts = firebase_manager.get_data(FIREBASE_PATHS['saved_contracts'])
    if all_contracts:
        for contract_name, contract_data in all_contracts.items():
            if (contract_data.get('contract_info', {}).get('computer_id') == computer_id_or_name or
                contract_data.get('contract_info', {}).get('computer_name') == computer_id_or_name):
                return contract_data

    return None

def delete_saved_contract(computer_id_or_name):
    """حذف العقد المحفوظ من Firebase"""
    # العثور على اسم المستخدم الصحيح
    computer_name = None

    # إذا كان الإدخال اسم مستخدم
    if isinstance(computer_id_or_name, str) and not computer_id_or_name.isdigit():
        computer_name = computer_id_or_name
    else:
        # البحث عن اسم المستخدم بـ computer_id
        all_contracts = firebase_manager.get_data(FIREBASE_PATHS['saved_contracts'])
        if all_contracts:
            for contract_name, contract_data in all_contracts.items():
                if (contract_data.get('contract_info', {}).get('computer_id') == computer_id_or_name or
                    contract_data.get('contract_info', {}).get('computer_name') == computer_id_or_name):
                    computer_name = contract_name
                    break

    if not computer_name:
        return False

    path = f"{FIREBASE_PATHS['saved_contracts']}/{computer_name}"
    success = firebase_manager.delete_data(path)

    if success:
        # تحديث حالة وجود عقد محفوظ في بيانات المستخدمين
        computers = load_computers()
        if computer_name and computer_name in computers:
            computers[computer_name]['has_saved_contract'] = False
            save_computers(computers)

    return success

# ===== دوال مساعدة =====
def increment_user_contracts(computer_name):
    """زيادة عداد العقود للمستخدم"""
    computers = load_computers()
    if computer_name in computers:
        computers[computer_name]['contracts_count'] = computers[computer_name].get('contracts_count', 0) + 1
        save_computers(computers)

def check_permission(computer_name, permission):
    """التحقق من صلاحيات المستخدم"""
    computers = load_computers()
    if computer_name in computers:
        permissions = computers[computer_name].get('permissions', {})
        return permissions.get(permission, True)  # افتراضي: true
    return False

def check_if_serial_exists(serial_number):
    """التحقق من وجود رقم تسلسلي في العقود المحفوظة في Firebase"""
    try:
        all_contracts = firebase_manager.get_data(FIREBASE_PATHS['saved_contracts'])
        if all_contracts:
            for contract_name, contract_data in all_contracts.items():
                if contract_data.get('contract_info', {}).get('serial_number') == str(serial_number):
                    return True
        return False
    except Exception as e:
        print(f"خطأ في البحث عن الرقم التسلسلي في Firebase: {e}")
        return False

def update_user_permissions(computer_name, permissions):
    """تحديث صلاحيات المستخدم"""
    computers = load_computers()
    if computer_name in computers:
        computers[computer_name]['permissions'] = permissions
        save_computers(computers)
        return True
    return False

def check_if_serial_exists(serial_number):
    """التحقق من وجود رقم تسلسلي في العقود المحفوظة في Firebase"""
    try:
        all_contracts = firebase_manager.get_data(FIREBASE_PATHS['saved_contracts'])
        if all_contracts:
            for contract_name, contract_data in all_contracts.items():
                if contract_data.get('contract_info', {}).get('serial_number') == str(serial_number):
                    return True
        return False
    except Exception as e:
        print(f"خطأ في البحث عن الرقم التسلسلي في Firebase: {e}")
        return False

# ===== دوال إدارة العقود المبرمة السنوية =====
def get_finalized_contracts_by_year(year):
    """جلب جميع العقود المبرمة لسنة معينة"""
    try:
        path = f"finalized_contracts"
        all_contracts = firebase_manager.get_data(path)

        if not all_contracts:
            return {}

        year_contracts = {}
        for contract_id, contract_data in all_contracts.items():
            # استخراج السنة من تاريخ الإبرام
            finalized_at = contract_data.get('finalized_at', '')
            if finalized_at:
                try:
                    contract_year = datetime.strptime(finalized_at, '%Y-%m-%d %H:%M:%S').year
                    if contract_year == year:
                        year_contracts[contract_id] = contract_data
                except ValueError:
                    # في حالة تنسيق تاريخ مختلف، تجاهل العقد
                    continue

        return year_contracts
    except Exception as e:
        print(f"خطأ في جلب عقود السنة {year}: {e}")
        return {}

def move_contracts_to_annual_archive(year):
    """نقل عقود السنة إلى الأرشيف السنوي"""
    try:
        # جلب عقود السنة
        year_contracts = get_finalized_contracts_by_year(year)

        if not year_contracts:
            return False, "لا توجد عقود لهذه السنة"

        # حفظ العقود في الأرشيف السنوي
        archive_path = f"annual_archives/{year}"
        success = firebase_manager.set_data(archive_path, year_contracts)

        if success:
            return True, f"تم نقل {len(year_contracts)} عقد إلى أرشيف سنة {year}"
        else:
            return False, "فشل في نقل العقود إلى الأرشيف"

    except Exception as e:
        print(f"خطأ في نقل عقود السنة {year} إلى الأرشيف: {e}")
        return False, f"خطأ في النقل: {str(e)}"

def delete_finalized_contracts_by_year(year, admin_password):
    """حذف العقود المبرمة لسنة معينة بعد التأكد من كلمة مرور الإدارة"""
    try:
        # التحقق من كلمة مرور الإدارة
        admin_config = load_admin_config()
        if admin_password != admin_config.get('password'):
            return False, "كلمة مرور الإدارة غير صحيحة"

        # جلب عقود السنة
        year_contracts = get_finalized_contracts_by_year(year)

        if not year_contracts:
            return False, "لا توجد عقود لهذه السنة للحذف"

        # حذف كل عقد من العقود المبرمة
        deleted_count = 0
        for contract_id in year_contracts.keys():
            contract_path = f"finalized_contracts/{contract_id}"
            if firebase_manager.delete_data(contract_path):
                deleted_count += 1

        if deleted_count > 0:
            return True, f"تم حذف {deleted_count} عقد من سنة {year}"
        else:
            return False, "فشل في حذف العقود"

    except Exception as e:
        print(f"خطأ في حذف عقود السنة {year}: {e}")
        return False, f"خطأ في الحذف: {str(e)}"

def get_available_years():
    """جلب قائمة السنوات المتاحة للعقود المبرمة"""
    try:
        path = "finalized_contracts"
        all_contracts = firebase_manager.get_data(path)

        if not all_contracts:
            return []

        years = set()
        for contract_data in all_contracts.values():
            finalized_at = contract_data.get('finalized_at', '')
            if finalized_at:
                try:
                    year = datetime.strptime(finalized_at, '%Y-%m-%d %H:%M:%S').year
                    years.add(year)
                except ValueError:
                    continue

        return sorted(list(years), reverse=True)  # ترتيب تنازلي (الأحدث أولاً)
    except Exception as e:
        print(f"خطأ في جلب السنوات المتاحة: {e}")
        return []

def update_user_permissions(computer_name, permissions):
    """تحديث صلاحيات المستخدم"""
    computers = load_computers()
    if computer_name in computers:
        computers[computer_name]['permissions'] = permissions
        save_computers(computers)
        return True
    return False

def migrate_permissions():
    """إضافة الصلاحيات الجديدة للحاسبات الموجودة"""
    try:
        computers = load_computers()
        updated = False

        for computer_name, computer_data in computers.items():
            permissions = computer_data.get('permissions', {})

            # إضافة الصلاحيات الجديدة إذا لم تكن موجودة
            if 'edit_finalized_contracts' not in permissions:
                permissions['edit_finalized_contracts'] = False
                updated = True

            if 'download_finalized_contracts' not in permissions:
                permissions['download_finalized_contracts'] = False
                updated = True

            computers[computer_name]['permissions'] = permissions

        if updated:
            save_computers(computers)
            print("تم تحديث صلاحيات الحاسبات الموجودة")

        return True
    except Exception as e:
        print(f"خطأ في ترحيل الصلاحيات: {e}")
        return False
