تعليمات تشغيل سيرفر QR للعقود
=====================================

تم حل مشكلة Firebase وإزالة جميع ملفات Docker كما طلبت.

طرق التشغيل:
-----------

1. الطريقة السهلة (Windows):
   - انقر مرتين على ملف: start_simple.bat
   - سيتم تثبيت Flask تلقائياً إذا لم يكن مثبتاً
   - سيتم تشغيل السيرفر على المنفذ 21226

2. الطريقة اليدوية:
   - افتح Command Prompt أو PowerShell
   - انتقل إلى مجلد QR
   - اكتب: python run_simple.py

3. التشغيل المباشر:
   - python app.py

الوصول للسيرفر:
--------------
- الصفحة الرئيسية: http://localhost:21226
- عقد تجريبي: http://localhost:21226/contract/12345678-1234-1234-1234-123456789abc

ملف البيانات:
-----------
- يتم قراءة العقود من: contracts_data.json
- إذا لم يكن موجوداً، سيتم إنشاؤه تلقائياً مع بيانات تجريبية

الحماية المطبقة:
---------------
- Rate limiting (100 طلب في الدقيقة)
- التحقق من صحة UUID
- منع الوصول المباشر للملفات
- Headers أمان إضافية
- منع النسخ والحفظ في صفحة العقد

الملفات المهمة:
--------------
- app.py: التطبيق الرئيسي
- run_simple.py: ملف التشغيل المحسن
- start_simple.bat: تشغيل سهل لـ Windows
- contracts_data.json: ملف البيانات
- templates/: قوالب HTML

ملاحظات:
--------
- لا يحتاج Firebase أو Docker
- يعمل محلياً فقط
- البيانات في ملف JSON محلي
- لإيقاف السيرفر: Ctrl+C

تم حل المشكلة بنجاح! ✅
