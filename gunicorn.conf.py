# إعدادات Gunicorn محسنة للسيرفر البسيط
# تهدف لتقليل استهلاك CPU والذاكرة

import os
import multiprocessing

# إعدادات الخادم الأساسية
bind = "0.0.0.0:21717"
backlog = 512

# إعدادات العمال - محسنة للسيرفر البسيط
# استخدام عامل واحد فقط لتقليل استهلاك الموارد
workers = 1
worker_class = "sync"
worker_connections = 100  # تقليل عدد الاتصالات المتزامنة
max_requests = 500  # إعادة تشغيل العامل بعد 500 طلب لتجنب تسريب الذاكرة
max_requests_jitter = 50  # إضافة عشوائية لتجنب إعادة التشغيل المتزامن

# إعدادات المهلة الزمنية - محسنة للأداء
timeout = 60  # تقليل timeout لتجنب العمليات المعلقة
keepalive = 5  # تقليل keepalive لتوفير الموارد
graceful_timeout = 30

# إعدادات الذاكرة والأداء
preload_app = True  # تحميل التطبيق مسبقاً لتوفير الذاكرة
max_worker_memory = 400  # حد أقصى 400 ميجابايت لكل عامل

# إعدادات التسجيل - تقليل التسجيل لتوفير الموارد
loglevel = "warning"  # تسجيل التحذيرات والأخطاء فقط
access_log_format = '%(h)s "%(r)s" %(s)s %(b)s "%(f)s" %(D)s'
accesslog = None  # إيقاف access log لتوفير الموارد
errorlog = "-"  # الأخطاء إلى stderr

# إعدادات الأمان
limit_request_line = 2048  # تقليل حد طول السطر
limit_request_fields = 50  # تقليل عدد الحقول
limit_request_field_size = 4096  # تقليل حجم الحقل

# إعدادات إضافية لتحسين الأداء
enable_stdio_inheritance = True
reuse_port = True  # إعادة استخدام المنفذ لتحسين الأداء

# دالة لمراقبة استهلاك الذاكرة
def when_ready(server):
    """يتم استدعاؤها عند جاهزية الخادم"""
    server.log.info("🚀 خادم Gunicorn جاهز مع إعدادات محسنة للسيرفر البسيط")
    server.log.info(f"📊 عدد العمال: {workers}")
    server.log.info(f"💾 حد الذاكرة لكل عامل: {max_worker_memory} ميجابايت")

def worker_int(worker):
    """يتم استدعاؤها عند مقاطعة العامل"""
    worker.log.info("⚠️ تم مقاطعة العامل - تنظيف الموارد")

def pre_fork(server, worker):
    """يتم استدعاؤها قبل إنشاء عامل جديد"""
    server.log.info(f"🔄 إنشاء عامل جديد: {worker.pid}")

def post_fork(server, worker):
    """يتم استدعاؤها بعد إنشاء عامل جديد"""
    server.log.info(f"✅ تم إنشاء العامل: {worker.pid}")
    
    # تحسينات إضافية للعامل الجديد
    try:
        import gc
        gc.set_threshold(700, 10, 10)  # تحسين garbage collection
        server.log.info("🧹 تم تحسين إعدادات garbage collection")
    except Exception as e:
        server.log.warning(f"⚠️ فشل في تحسين garbage collection: {e}")

def worker_abort(worker):
    """يتم استدعاؤها عند إنهاء العامل بشكل مفاجئ"""
    worker.log.error(f"❌ تم إنهاء العامل بشكل مفاجئ: {worker.pid}")

# إعدادات متقدمة لتحسين الأداء على السيرفر البسيط
def post_worker_init(worker):
    """تحسينات إضافية بعد تهيئة العامل"""
    try:
        # تحسين إعدادات Python للأداء
        import sys
        sys.setcheckinterval(1000)  # تقليل فحص الخيوط
        
        # تحسين garbage collection
        import gc
        gc.disable()  # إيقاف garbage collection التلقائي
        
        worker.log.info("⚡ تم تطبيق تحسينات الأداء على العامل")
    except Exception as e:
        worker.log.warning(f"⚠️ فشل في تطبيق تحسينات الأداء: {e}")

# إعدادات البيئة
raw_env = [
    'PYTHONUNBUFFERED=1',
    'PYTHONOPTIMIZE=1',  # تحسين Python
]

# تحديد متغيرات البيئة المطلوبة
forwarded_allow_ips = '*'
secure_scheme_headers = {
    'X-FORWARDED-PROTOCOL': 'ssl',
    'X-FORWARDED-PROTO': 'https',
    'X-FORWARDED-SSL': 'on'
}
