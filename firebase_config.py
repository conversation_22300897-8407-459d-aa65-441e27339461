# إعدادات Firebase للمشروع
import json
import os

# قراءة إعدادات Firebase من ملف firebase.txt
def get_firebase_config():
    """قراءة إعدادات Firebase من ملف firebase.txt"""
    try:
        with open('firebase.txt', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # استخراج JSON الأول (Service Account)
        lines = content.split('\n')
        json_start = -1
        json_end = -1
        
        for i, line in enumerate(lines):
            if line.strip().startswith('{') and json_start == -1:
                json_start = i
            if line.strip().endswith('}') and json_start != -1:
                json_end = i
                break
        
        if json_start != -1 and json_end != -1:
            json_content = '\n'.join(lines[json_start:json_end+1])
            service_account = json.loads(json_content)
            return service_account
        else:
            raise ValueError("لم يتم العثور على JSON صالح في ملف firebase.txt")
            
    except FileNotFoundError:
        raise FileNotFoundError("ملف firebase.txt غير موجود")
    except json.JSONDecodeError as e:
        raise ValueError(f"خطأ في تحليل JSON: {e}")

# إعدادات قاعدة البيانات
DATABASE_URL = "https://car-report-a01fe-default-rtdb.firebaseio.com"

# مسارات البيانات في Firebase
FIREBASE_PATHS = {
    'computers': 'computers',
    'serial_number': 'serial_number',
    'admin_config': 'admin_config',
    'saved_contracts': 'saved_contracts'
}
