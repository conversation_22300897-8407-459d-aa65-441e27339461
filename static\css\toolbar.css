/* تنسيق التول بار الموحد لجميع الصفحات */
/* إعادة تعيين التنسيقات للتول بار لتجنب التداخل */
.custom-toolbar * {
    box-sizing: border-box !important;
}

.custom-toolbar a,
.custom-toolbar button {
    background: none !important;
    border: none !important;
    color: inherit !important;
    text-decoration: none !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    transform: none !important;
    transition: none !important;
}

.custom-toolbar .user-info,
.custom-toolbar .dropdown-item,
.custom-toolbar .office-logo {
    background: initial !important;
    border: initial !important;
    color: initial !important;
    padding: initial !important;
    margin: initial !important;
    font-size: initial !important;
    font-weight: initial !important;
    border-radius: initial !important;
    box-shadow: initial !important;
    transform: initial !important;
    transition: initial !important;
}
.custom-toolbar {
    background: #ffffff !important;
    color: #2c3e50 !important;
    padding: 0 !important;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
    border-bottom: 1px solid #e8e9ea !important;
    width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    left: 0 !important;
    right: 0 !important;
}

.toolbar-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    height: 70px;
    width: 100%;
    box-sizing: border-box;
}

.toolbar-right {
    position: relative;
}

.user-info {
    display: flex !important;
    align-items: center !important;
    cursor: pointer !important;
    padding: 10px 18px !important;
    border-radius: 30px !important;
    transition: all 0.3s ease !important;
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    direction: rtl !important;
    color: #2c3e50 !important;
}

.user-info:hover {
    background: #e9ecef !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
    color: #2c3e50 !important;
}

.user-avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    font-size: 18px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.15);
    border: 2px solid #ffffff;
}

.admin-icon {
    color: #ffd700;
    filter: drop-shadow(0 0 4px rgba(255,215,0,0.6));
}

.user-icon {
    color: white;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-left: 10px;
}

.user-name {
    font-weight: 600;
    font-size: 15px;
    color: #2c3e50;
}

.user-type {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

.dropdown-arrow {
    margin-left: 10px;
    font-size: 11px;
    transition: transform 0.3s ease;
    color: #6c757d;
}

.user-info.active .dropdown-arrow {
    transform: rotate(180deg) !important;
}

.user-dropdown {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 12px 35px rgba(0,0,0,0.1) !important;
    min-width: 260px !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: 1px solid #dee2e6 !important;
    margin-top: 10px !important;
    overflow: hidden !important;
    direction: rtl !important;
}

.user-dropdown.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    display: block !important;
    z-index: 9999 !important;
}

.dropdown-item {
    display: flex !important;
    align-items: center !important;
    padding: 16px 20px !important;
    color: #495057 !important;
    text-decoration: none !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-bottom: 1px solid #f1f3f4 !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    position: relative !important;
    background: transparent !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    color: #2c3e50 !important;
    transform: translateX(-2px) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05) !important;
    text-decoration: none !important;
}

.dropdown-item:last-child {
    border-bottom: none;
    border-radius: 0 0 12px 12px;
}

.dropdown-item:first-child {
    border-radius: 12px 12px 0 0;
}

.logout-item {
    border-top: 1px solid #e9ecef;
    margin-top: 4px;
}

.logout-item:hover {
    background: linear-gradient(135deg, #f1f3f4, #e8eaed);
    color: #2c3e50;
}

.item-icon {
    margin-left: 14px;
    font-size: 16px;
    width: 24px;
    text-align: center;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.dropdown-item:hover .item-icon {
    opacity: 1;
}

.dropdown-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #e9ecef, transparent);
    margin: 6px 0;
}

.toolbar-left {
    display: flex;
    align-items: center;
}

.office-logo {
    display: flex;
    align-items: center;
    font-weight: 600;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    direction: rtl;
}

.office-logo:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-decoration: none;
    color: inherit;
}

.logo-icon {
    font-size: 26px;
    margin-left: 12px;
    color: #007bff;
}

.office-name {
    font-size: 20px;
    color: #2c3e50;
    font-weight: 600;
}

/* تأثيرات متجاوبة */
@media (max-width: 768px) {
    .toolbar-container {
        padding: 0 20px;
        height: 65px;
    }

    .office-name {
        font-size: 18px;
    }

    .user-details {
        display: none;
    }

    .user-dropdown {
        min-width: 220px;
        right: -10px;
    }
}

@media (max-width: 480px) {
    .toolbar-container {
        padding: 0 15px;
    }

    .logo-icon {
        font-size: 22px;
    }

    .office-name {
        font-size: 16px;
    }
}

/* تنسيقات إضافية لضمان عمل القائمة المنسدلة */
.custom-toolbar .user-dropdown {
    pointer-events: auto !important;
    cursor: pointer !important;
}

.custom-toolbar .user-dropdown.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    display: block !important;
    z-index: 9999 !important;
    pointer-events: auto !important;
}

.custom-toolbar .dropdown-item {
    pointer-events: auto !important;
    cursor: pointer !important;
}

.custom-toolbar .user-info {
    pointer-events: auto !important;
    cursor: pointer !important;
}
