#!/bin/bash

# QR Contract Viewer - Installation Script
# ========================================

echo "🚀 تثبيت QR Contract Viewer"
echo "=========================="

# التحقق من Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 غير مثبت!"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

echo "✅ تم العثور على Python: $(python3 --version)"

# إنشاء البيئة الافتراضية
echo "📦 إنشاء البيئة الافتراضية..."
python3 -m venv venv

# تفعيل البيئة الافتراضية
echo "🔧 تفعيل البيئة الافتراضية..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    source venv/Scripts/activate
else
    source venv/bin/activate
fi

# ترقية pip
echo "⬆️ ترقية pip..."
pip install --upgrade pip

# تثبيت المتطلبات
echo "📚 تثبيت المتطلبات..."
pip install -r requirements.txt

# التحقق من ملف البيئة
if [ ! -f .env ]; then
    echo "⚠️ ملف .env غير موجود!"
    echo "يرجى إنشاء ملف .env مع متغيرات Firebase المطلوبة"
    echo "راجع ملف FIREBASE_SETUP.md للتفاصيل"
else
    echo "✅ ملف .env موجود بالفعل"
fi

# التحقق من Firebase
echo "🔥 التحقق من إعدادات Firebase..."
python3 -c "
try:
    from firebase_admin_config import firebase_manager
    if firebase_manager.initialize():
        print('✅ Firebase تم تهيئته بنجاح')
    else:
        print('⚠️ فشل في تهيئة Firebase - تحقق من ملف .env')
except ImportError:
    print('⚠️ مكتبات Firebase غير مثبتة - تم تثبيتها الآن')
except Exception as e:
    print(f'⚠️ خطأ في Firebase: {e}')
"

echo ""
echo "🎉 تم التثبيت بنجاح!"
echo ""
echo "الخطوات التالية:"
echo "1. تأكد من صحة ملف .env مع متغيرات Firebase"
echo "2. راجع ملف FIREBASE_SETUP.md للتفاصيل"
echo "3. تشغيل التطبيق: python run.py"
echo ""
echo "الملفات المهمة:"
echo "- README.md: دليل التشغيل العام"
echo "- FIREBASE_SETUP.md: دليل إعداد Firebase"
echo "- .env: متغيرات البيئة (محمي)"
