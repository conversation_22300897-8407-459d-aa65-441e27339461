# QR Contract Viewer 📱

سيرفر Flask منفصل لعرض العقود من Firebase عند قراءة QR Code بأقصى درجات الحماية والأمان.

## 🚀 التثبيت السريع

### Windows:
```cmd
install.bat
```

### Linux/Mac:
```bash
chmod +x install.sh
./install.sh
```

### التثبيت اليدوي:

1. **إنشاء البيئة الافتراضية:**
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate.bat  # Windows
```

2. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

3. **إعداد متغيرات البيئة:**
```bash
cp .env.example .env
# ثم تعديل ملف .env بالقيم الصحيحة
```

## ⚙️ الإعداد

### 1. إعداد Firebase

احصل على Service Account من Firebase Console:
1. اذهب إلى Firebase Console
2. اختر مشروعك
3. Project Settings > Service Accounts
4. Generate New Private Key
5. انسخ القيم إلى ملف `.env`

### 2. ملف `.env`

```env
# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com/

# Flask Configuration
SECRET_KEY=your-very-secret-key-here-change-this-in-production
FLASK_ENV=production
PORT=21226
```

## 🏃‍♂️ تشغيل السيرفر

### للتطوير:
```bash
python run.py
```

### للإنتاج مع Gunicorn:
```bash
gunicorn wsgi:application --bind 0.0.0.0:21226 --workers 4
```

### للإنتاج مع PM2:
```bash
pm2 start wsgi.py --name qr-viewer --interpreter python3
```

## 🔒 الميزات الأمنية

- ✅ **Rate Limiting**: حماية من كثرة الطلبات (100 طلب/دقيقة)
- ✅ **UUID Validation**: التحقق من صحة معرفات العقود
- ✅ **XSS Protection**: حماية من هجمات Cross-Site Scripting
- ✅ **CSRF Protection**: حماية من هجمات Cross-Site Request Forgery
- ✅ **Security Headers**: إضافة headers الأمان المطلوبة
- ✅ **Input Validation**: التحقق من صحة جميع المدخلات
- ✅ **Logging**: تسجيل جميع العمليات والوصولات
- ✅ **Environment Variables**: حماية المعلومات الحساسة
- ✅ **No Indexing**: منع فهرسة الصفحات من محركات البحث
- ✅ **Copy Protection**: منع النسخ والحفظ من المتصفح

## 📁 هيكل المشروع

```
QR/
├── app.py              # التطبيق الرئيسي
├── run.py              # ملف تشغيل التطبيق
├── wsgi.py             # نقطة دخول WSGI
├── requirements.txt    # المتطلبات
├── .env.example        # مثال متغيرات البيئة
├── Procfile           # إعداد Heroku
├── runtime.txt        # إصدار Python
├── install.sh         # سكريبت التثبيت (Linux/Mac)
├── install.bat        # سكريبت التثبيت (Windows)
├── templates/         # قوالب HTML
│   ├── base.html     # القالب الأساسي
│   ├── index.html    # الصفحة الرئيسية
│   ├── contract.html # عرض العقد
│   ├── 404.html      # صفحة العقد غير موجود
│   └── 500.html      # صفحة خطأ السيرفر
└── README.md         # هذا الملف
```

## 🔗 الاستخدام

1. **إنشاء QR Code**: يتم إنشاء QR Code يحتوي على رابط مثل:
   ```
   https://425243.site.bot-hosting.net/contract/12345678-1234-1234-1234-123456789abc
   ```

2. **مسح QR Code**: المستخدم يمسح الرمز بكاميرا الهاتف

3. **عرض العقد**: يتم توجيهه تلقائياً لصفحة العقد الآمنة

4. **طباعة العقد**: يمكن طباعة العقد مباشرة من المتصفح

## 🛡️ الأمان المتقدم

### حماية الشبكة:
- استخدام HTTPS إجباري في الإنتاج
- تشفير جميع الاتصالات
- حماية من هجمات Man-in-the-Middle

### حماية التطبيق:
- تسجيل جميع محاولات الوصول
- مراقبة الأنشطة المشبوهة
- حماية من SQL Injection (غير مطبق لعدم وجود SQL)
- حماية من Path Traversal

### حماية البيانات:
- عدم تخزين بيانات حساسة محلياً
- استخدام Firebase للتخزين الآمن
- تشفير متغيرات البيئة

## 🚀 النشر

### Heroku:
```bash
# إنشاء تطبيق Heroku
heroku create your-app-name

# إضافة متغيرات البيئة
heroku config:set FIREBASE_PROJECT_ID=your-project-id
heroku config:set FIREBASE_PRIVATE_KEY="your-private-key"
# ... باقي المتغيرات

# النشر
git push heroku main
```

### VPS/Dedicated Server:
```bash
# استخدام systemd service
sudo cp qr-viewer.service /etc/systemd/system/
sudo systemctl enable qr-viewer
sudo systemctl start qr-viewer
```

### Docker:
```bash
# بناء الصورة
docker build -t qr-viewer .

# تشغيل الحاوية
docker run -d -p 21226:21226 --env-file .env qr-viewer
```

## 📊 المراقبة والصيانة

### فحص صحة السيرفر:
```bash
curl http://localhost:21226/health
```

### مراقبة السجلات:
```bash
tail -f logs/app.log
```

### النسخ الاحتياطي:
- نسخ احتياطي دوري من Firebase
- نسخ احتياطي من ملفات الإعداد
- مراقبة مساحة التخزين

## ⚠️ ملاحظات مهمة

1. **HTTPS إجباري**: يجب استخدام HTTPS في الإنتاج
2. **حماية .env**: تأكد من عدم رفع ملف `.env` إلى Git
3. **مراقبة السجلات**: راقب السجلات بانتظام للأنشطة المشبوهة
4. **تحديث المتطلبات**: حدث المكتبات بانتظام للأمان
5. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية من Firebase
6. **اختبار الأمان**: اختبر التطبيق بانتظام للثغرات الأمنية

## 🆘 استكشاف الأخطاء

### خطأ في الاتصال بـ Firebase:
```bash
# تحقق من متغيرات البيئة
echo $FIREBASE_PROJECT_ID

# تحقق من صحة Service Account
python -c "import firebase_admin; print('Firebase OK')"
```

### خطأ في المنفذ:
```bash
# تحقق من المنافذ المستخدمة
netstat -tulpn | grep 21226

# تغيير المنفذ
export PORT=8080
```

### مشاكل الأداء:
```bash
# زيادة عدد العمليات
gunicorn wsgi:application --workers 8

# مراقبة استخدام الذاكرة
htop
```

## 📞 الدعم

للدعم التقني أو الإبلاغ عن مشاكل:
- تحقق من السجلات أولاً
- تأكد من صحة إعدادات Firebase
- تحقق من اتصال الإنترنت
- راجع هذا الدليل للحلول الشائعة
