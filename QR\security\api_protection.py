"""
Advanced API Protection System
نظام حماية API المتقدم

This module provides comprehensive API security including rate limiting,
input validation, and injection attack prevention
يوفر هذا الملف حماية شاملة للـ API
"""

import re
import time
import json
import hashlib
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, current_app
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

class APIProtection:
    """
    Advanced API protection with multiple security layers
    حماية API متقدمة مع طبقات أمان متعددة
    """
    
    def __init__(self):
        self.rate_limits = defaultdict(list)  # In production, use Redis
        self.blocked_ips = set()
        self.suspicious_patterns = [
            # SQL Injection patterns
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
            r"(--|#|/\*|\*/)",
            r"(\bxp_cmdshell\b|\bsp_executesql\b)",
            
            # XSS patterns
            r"(<script[^>]*>.*?</script>)",
            r"(javascript:|vbscript:|onload=|onerror=|onclick=)",
            r"(<iframe|<object|<embed|<applet)",
            
            # Path traversal
            r"(\.\./|\.\.\\\|%2e%2e%2f|%2e%2e%5c)",
            
            # Command injection
            r"(;|\||&|`|\$\(|\${)",
            r"(\b(cat|ls|dir|type|echo|ping|wget|curl|nc|netcat)\b)",
            
            # LDAP injection
            r"(\*|\(|\)|&|\|)",
            
            # NoSQL injection
            r"(\$where|\$ne|\$gt|\$lt|\$regex)",
        ]
        
        # Compile patterns for better performance
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.suspicious_patterns]
    
    def check_rate_limit(self, identifier: str, limit: int = 100, window: int = 3600) -> bool:
        """
        Check if request is within rate limit
        فحص إذا كان الطلب ضمن الحد المسموح
        """
        try:
            current_time = time.time()
            window_start = current_time - window
            
            # Clean old entries
            self.rate_limits[identifier] = [
                timestamp for timestamp in self.rate_limits[identifier]
                if timestamp > window_start
            ]
            
            # Check if limit exceeded
            if len(self.rate_limits[identifier]) >= limit:
                logger.warning(f"🚫 Rate limit exceeded for: {identifier}")
                return False
            
            # Add current request
            self.rate_limits[identifier].append(current_time)
            return True
            
        except Exception as e:
            logger.error(f"❌ Rate limit check failed: {e}")
            return True  # Allow on error
    
    def validate_input(self, data: str, max_length: int = 10000) -> bool:
        """
        Validate input for malicious patterns
        التحقق من صحة المدخلات للأنماط الضارة
        """
        try:
            if not data:
                return True
            
            # Check length
            if len(data) > max_length:
                logger.warning(f"⚠️ Input too long: {len(data)} characters")
                return False
            
            # Check for suspicious patterns
            for pattern in self.compiled_patterns:
                if pattern.search(data):
                    logger.warning(f"🚨 Suspicious pattern detected: {pattern.pattern}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Input validation failed: {e}")
            return False
    
    def sanitize_input(self, data: str) -> str:
        """
        Sanitize input data
        تنظيف البيانات المدخلة
        """
        try:
            if not isinstance(data, str):
                data = str(data)
            
            # Remove null bytes
            data = data.replace('\x00', '')
            
            # Escape HTML entities
            data = data.replace('&', '&amp;')
            data = data.replace('<', '&lt;')
            data = data.replace('>', '&gt;')
            data = data.replace('"', '&quot;')
            data = data.replace("'", '&#x27;')
            
            # Remove control characters
            data = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', data)
            
            return data.strip()
            
        except Exception as e:
            logger.error(f"❌ Input sanitization failed: {e}")
            return ""
    
    def detect_anomaly(self, request_data: dict) -> bool:
        """
        Detect anomalous request patterns
        اكتشاف أنماط الطلبات الشاذة
        """
        try:
            # Check for unusual request size
            request_size = len(json.dumps(request_data))
            if request_size > 100000:  # 100KB
                logger.warning(f"⚠️ Unusually large request: {request_size} bytes")
                return True
            
            # Check for too many parameters
            if len(request_data) > 50:
                logger.warning(f"⚠️ Too many parameters: {len(request_data)}")
                return True
            
            # Check for deeply nested structures
            def check_depth(obj, depth=0):
                if depth > 10:
                    return True
                if isinstance(obj, dict):
                    return any(check_depth(v, depth + 1) for v in obj.values())
                elif isinstance(obj, list):
                    return any(check_depth(item, depth + 1) for item in obj)
                return False
            
            if check_depth(request_data):
                logger.warning("⚠️ Deeply nested request structure")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Anomaly detection failed: {e}")
            return False
    
    def get_client_fingerprint(self) -> str:
        """
        Generate unique client fingerprint
        إنشاء بصمة فريدة للعميل
        """
        try:
            # Collect client information
            ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            user_agent = request.headers.get('User-Agent', '')
            accept_language = request.headers.get('Accept-Language', '')
            accept_encoding = request.headers.get('Accept-Encoding', '')
            
            # Create fingerprint
            fingerprint_data = f"{ip}:{user_agent}:{accept_language}:{accept_encoding}"
            fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()[:16]
            
            return fingerprint
            
        except Exception as e:
            logger.error(f"❌ Failed to generate fingerprint: {e}")
            return 'unknown'
    
    def log_security_event(self, event_type: str, details: dict):
        """
        Log security events for monitoring
        تسجيل الأحداث الأمنية للمراقبة
        """
        try:
            security_log = {
                'timestamp': datetime.utcnow().isoformat(),
                'event_type': event_type,
                'ip': request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr),
                'user_agent': request.headers.get('User-Agent', ''),
                'endpoint': request.endpoint,
                'method': request.method,
                'details': details
            }
            
            logger.warning(f"🔒 Security Event: {json.dumps(security_log)}")
            
        except Exception as e:
            logger.error(f"❌ Failed to log security event: {e}")

# Global protection instance
api_protection = APIProtection()

# Decorators for API protection
def rate_limit(limit: int = 100, window: int = 3600):
    """Decorator for rate limiting"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                fingerprint = api_protection.get_client_fingerprint()
                
                if not api_protection.check_rate_limit(fingerprint, limit, window):
                    api_protection.log_security_event('rate_limit_exceeded', {
                        'limit': limit,
                        'window': window,
                        'fingerprint': fingerprint
                    })
                    return jsonify({'error': 'Rate limit exceeded'}), 429
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"❌ Rate limiting error: {e}")
                return f(*args, **kwargs)  # Continue on error
        
        return decorated_function
    return decorator

def validate_json_input(max_length: int = 10000):
    """Decorator for JSON input validation"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                if request.is_json:
                    data = request.get_json()
                    
                    # Convert to string for validation
                    data_str = json.dumps(data)
                    
                    if not api_protection.validate_input(data_str, max_length):
                        api_protection.log_security_event('malicious_input_detected', {
                            'data_length': len(data_str),
                            'endpoint': request.endpoint
                        })
                        return jsonify({'error': 'Invalid input detected'}), 400
                    
                    # Check for anomalies
                    if api_protection.detect_anomaly(data):
                        api_protection.log_security_event('anomalous_request', {
                            'data_size': len(data_str),
                            'param_count': len(data) if isinstance(data, dict) else 0
                        })
                        return jsonify({'error': 'Anomalous request detected'}), 400
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"❌ Input validation error: {e}")
                return jsonify({'error': 'Input validation failed'}), 400
        
        return decorated_function
    return decorator

def sanitize_form_input(f):
    """Decorator to sanitize form input"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            if request.form:
                # Sanitize form data
                sanitized_form = {}
                for key, value in request.form.items():
                    sanitized_key = api_protection.sanitize_input(key)
                    sanitized_value = api_protection.sanitize_input(value)
                    sanitized_form[sanitized_key] = sanitized_value
                
                # Replace request.form with sanitized version
                request.form = sanitized_form
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"❌ Form sanitization error: {e}")
            return f(*args, **kwargs)  # Continue on error
    
    return decorated_function

def block_suspicious_requests(f):
    """Decorator to block suspicious requests"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Check if IP is blocked
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            if client_ip in api_protection.blocked_ips:
                api_protection.log_security_event('blocked_ip_access', {
                    'ip': client_ip
                })
                return jsonify({'error': 'Access denied'}), 403
            
            # Check request headers for suspicious patterns
            for header_name, header_value in request.headers:
                if not api_protection.validate_input(header_value, 1000):
                    api_protection.log_security_event('malicious_header', {
                        'header': header_name,
                        'value': header_value[:100]  # Log first 100 chars only
                    })
                    return jsonify({'error': 'Malicious request detected'}), 400
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"❌ Suspicious request check error: {e}")
            return f(*args, **kwargs)  # Continue on error
    
    return decorated_function
