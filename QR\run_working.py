#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QR Application - Working Version
تشغيل التطبيق بحماية معقولة

This version works properly with reasonable security without blocking normal requests.
"""

import os
import sys
import secrets
import logging
from dotenv import load_dotenv

def setup_logging():
    """إعداد التسجيل البسيط"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('app.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def update_env_file():
    """تحديث ملف .env بمفاتيح آمنة"""
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # توليد مفاتيح آمنة جديدة
        jwt_secret = secrets.token_urlsafe(64)
        csrf_secret = secrets.token_urlsafe(64)
        master_key = secrets.token_urlsafe(64)
        flask_secret = secrets.token_urlsafe(64)
        
        # استبدال القيم غير الآمنة
        content = content.replace('your-jwt-secret-key-change-this-in-production', jwt_secret)
        content = content.replace('your-csrf-secret-key-change-this-in-production', csrf_secret)
        content = content.replace('your-master-encryption-key-change-this-in-production', master_key)
        content = content.replace('qr-viewer-secret-key-2024-firebase-enhanced', flask_secret)
        
        # حفظ الملف المحدث
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("[SUCCESS] تم توليد مفاتيح الأمان الجديدة")
        return True
    except Exception as e:
        print(f"[ERROR] خطأ في تحديث ملف .env: {e}")
        return False

def create_simple_app():
    """إنشاء تطبيق بسيط مع حماية معقولة"""
    from flask import Flask, render_template, request, jsonify, abort
    import json
    import uuid
    import time
    from functools import wraps
    
    app = Flask(__name__)
    app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', secrets.token_urlsafe(32))
    
    # إعداد Firebase بسيط
    try:
        import firebase_admin
        from firebase_admin import credentials, db
        
        # إعداد Firebase
        if not firebase_admin._apps:
            firebase_config = {
                "type": os.getenv('FIREBASE_TYPE'),
                "project_id": os.getenv('FIREBASE_PROJECT_ID'),
                "private_key_id": os.getenv('FIREBASE_PRIVATE_KEY_ID'),
                "private_key": os.getenv('FIREBASE_PRIVATE_KEY', '').replace('\\n', '\n'),
                "client_email": os.getenv('FIREBASE_CLIENT_EMAIL'),
                "client_id": os.getenv('FIREBASE_CLIENT_ID'),
                "auth_uri": os.getenv('FIREBASE_AUTH_URI'),
                "token_uri": os.getenv('FIREBASE_TOKEN_URI'),
                "auth_provider_x509_cert_url": os.getenv('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
                "client_x509_cert_url": os.getenv('FIREBASE_CLIENT_X509_CERT_URL'),
                "universe_domain": os.getenv('FIREBASE_UNIVERSE_DOMAIN')
            }
            
            cred = credentials.Certificate(firebase_config)
            firebase_admin.initialize_app(cred, {
                'databaseURL': 'https://car-report-a01fe-default-rtdb.firebaseio.com'
            })
            print("[SUCCESS] تم تهيئة Firebase بنجاح")
    except Exception as e:
        print(f"[WARNING] تحذير Firebase: {e}")
        print("[INFO] سيتم استخدام الملف المحلي كبديل")
    
    # Rate limiting بسيط
    request_counts = {}
    
    def simple_rate_limit(max_requests=100, window=60):
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
                current_time = time.time()
                
                # تنظيف الطلبات القديمة
                if client_ip in request_counts:
                    request_counts[client_ip] = [
                        req_time for req_time in request_counts[client_ip] 
                        if current_time - req_time < window
                    ]
                else:
                    request_counts[client_ip] = []
                
                # فحص الحد الأقصى
                if len(request_counts[client_ip]) >= max_requests:
                    return jsonify({'error': 'Too many requests'}), 429
                
                request_counts[client_ip].append(current_time)
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def validate_uuid(uuid_string):
        """التحقق من صحة UUID"""
        try:
            uuid.UUID(uuid_string)
            return True
        except ValueError:
            return False
    
    def get_contract(contract_uuid):
        """جلب العقد من Firebase أو الملف المحلي"""
        try:
            # محاولة جلب من Firebase أولاً - البحث في finalized_contracts
            ref = db.reference('finalized_contracts')
            all_contracts = ref.get()

            if all_contracts:
                # البحث عن العقد بـ UUID
                for contract_id, contract_data in all_contracts.items():
                    if contract_data and isinstance(contract_data, dict):
                        # البحث في contract_data إذا كان العقد محفوظ بهذا التنسيق
                        if contract_data.get('uuid') == contract_uuid:
                            # إرجاع بيانات العقد الفعلية مع سجل التعديلات
                            if 'contract_data' in contract_data:
                                contract_info = contract_data['contract_data']
                                # إضافة سجل التعديلات إلى بيانات العقد
                                contract_info['modifications_history'] = contract_data.get('modifications_history', [])
                                return contract_info
                            else:
                                # إضافة سجل التعديلات إلى بيانات العقد
                                contract_data['modifications_history'] = contract_data.get('modifications_history', [])
                                return contract_data

                        # البحث في contract_data المدمجة
                        nested_contract = contract_data.get('contract_data', {})
                        if nested_contract and isinstance(nested_contract, dict) and nested_contract.get('uuid') == contract_uuid:
                            # إضافة سجل التعديلات إلى بيانات العقد
                            nested_contract['modifications_history'] = contract_data.get('modifications_history', [])
                            return nested_contract
        except:
            pass

        # إذا فشل Firebase، استخدم الملف المحلي
        try:
            with open('contracts.json', 'r', encoding='utf-8') as f:
                contracts = json.load(f)
                return contracts.get(contract_uuid)
        except:
            return None
    
    # Routes
    @app.route('/')
    @simple_rate_limit(max_requests=50, window=60)
    def index():
        """الصفحة الرئيسية"""
        return render_template('index.html')
    
    @app.route('/contract/<contract_uuid>')
    @simple_rate_limit(max_requests=30, window=60)
    def view_contract(contract_uuid):
        """عرض العقد"""
        try:
            # التحقق من صحة UUID
            if not validate_uuid(contract_uuid):
                abort(404)
            
            # جلب العقد
            contract_data = get_contract(contract_uuid)
            
            if not contract_data:
                abort(404)
            
            # التحقق من صحة البيانات
            if not isinstance(contract_data, dict) or not contract_data.get('uuid'):
                abort(404)
            
            return render_template('contract.html', contract=contract_data)
            
        except Exception as e:
            print(f"[ERROR] خطأ في عرض العقد {contract_uuid}: {e}")
            abort(500)
    
    @app.route('/health')
    def health_check():
        """فحص صحة التطبيق"""
        return jsonify({
            'status': 'healthy',
            'timestamp': time.time(),
            'version': '1.0.0'
        })
    
    @app.errorhandler(404)
    def not_found(error):
        return render_template('404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('500.html'), 500
    
    return app

def main():
    """تشغيل التطبيق"""
    try:
        print("=" * 60)
        print("QR Application - Working Version")
        print("تطبيق QR - نسخة تعمل بدون مشاكل")
        print("=" * 60)
        
        # إعداد التسجيل
        setup_logging()
        
        # التحقق من ملف .env
        if not os.path.exists('.env'):
            print("[ERROR] ملف .env غير موجود!")
            return
        
        # تحديث المفاتيح إذا لزم الأمر
        with open('.env', 'r', encoding='utf-8') as f:
            if 'your-' in f.read():
                print("[INFO] توليد مفاتيح أمان جديدة...")
                update_env_file()
        
        # تحميل متغيرات البيئة
        load_dotenv()
        
        print("[INFO] إنشاء التطبيق...")
        app = create_simple_app()
        
        # إعداد السيرفر
        port = int(os.environ.get('PORT', 21226))
        
        print(f"\n[SERVER] المنفذ: {port}")
        print(f"[SERVER] الرابط: http://localhost:{port}")
        print(f"[SERVER] فحص الصحة: http://localhost:{port}/health")
        
        print("\n[SECURITY] الحماية المفعلة:")
        print("- Rate limiting معقول")
        print("- التحقق من UUID")
        print("- حماية من الأخطاء")
        print("- تسجيل الأحداث")
        
        print(f"\n[INFO] التطبيق جاهز على: http://localhost:{port}")
        print("[INFO] اضغط Ctrl+C لإيقاف السيرفر")
        print("=" * 60)
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n[INFO] تم إيقاف السيرفر")
    except Exception as e:
        print(f"[ERROR] خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
