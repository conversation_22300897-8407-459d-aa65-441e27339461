version: '3.8'

services:
  qr-viewer:
    build: .
    container_name: qr-contract-viewer
    ports:
      - "21226:21226"
    environment:
      - FLASK_ENV=production
      - PORT=21226
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:21226/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
    networks:
      - qr-network

networks:
  qr-network:
    driver: bridge
