#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Contract to Firebase
إضافة عقد إلى Firebase

هذا الملف لإضافة عقود إلى Firebase Firestore
"""

import os
import sys
import json
import uuid
import logging
from datetime import datetime
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_contract():
    """إنشاء عقد تجريبي"""
    contract_uuid = str(uuid.uuid4())
    
    contract_data = {
        "uuid": contract_uuid,
        "contract_number": "2024-001",
        "title": "عقد تجريبي للاختبار",
        "description": "هذا عقد تجريبي لاختبار النظام",
        "client_name": "شركة الاختبار المحدودة",
        "client_id": "1234567890",
        "amount": 50000.00,
        "currency": "SAR",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "status": "active",
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "terms": [
            "الدفع خلال 30 يوم من تاريخ الفاتورة",
            "العمل يبدأ خلال 7 أيام من توقيع العقد",
            "ضمان لمدة سنة واحدة"
        ],
        "attachments": [],
        "signatures": {
            "client_signed": True,
            "client_signature_date": "2024-01-01T10:00:00",
            "provider_signed": True,
            "provider_signature_date": "2024-01-01T11:00:00"
        },
        "metadata": {
            "created_by": "system",
            "version": "1.0",
            "source": "firebase_test"
        }
    }
    
    return contract_data

def add_contract_to_firestore(contract_data):
    """إضافة عقد إلى Firestore"""
    try:
        from firebase_admin_config import get_firestore_client
        
        db = get_firestore_client()
        if not db:
            logger.error("Failed to get Firestore client")
            return False
        
        # إضافة العقد إلى مجموعة contracts
        doc_ref = db.collection('contracts').document(contract_data['uuid'])
        doc_ref.set(contract_data)
        
        logger.info(f"Contract added successfully: {contract_data['uuid']}")
        return True
        
    except Exception as e:
        logger.error(f"Error adding contract to Firestore: {e}")
        return False

def migrate_local_contracts():
    """نقل العقود من الملف المحلي إلى Firebase"""
    try:
        # مسار ملف البيانات المحلي
        data_file = os.path.join(os.path.dirname(__file__), '..', 'contracts_data.json')
        
        if not os.path.exists(data_file):
            logger.warning(f"Local data file not found: {data_file}")
            return False
        
        # قراءة البيانات المحلية
        with open(data_file, 'r', encoding='utf-8') as f:
            local_data = json.load(f)
        
        finalized_contracts = local_data.get('finalized_contracts', {})
        
        from firebase_admin_config import get_firestore_client
        db = get_firestore_client()
        
        if not db:
            logger.error("Failed to get Firestore client")
            return False
        
        migrated_count = 0
        
        # نقل العقود من جميع السنوات
        for year, year_contracts in finalized_contracts.items():
            if isinstance(year_contracts, dict):
                for contract_id, contract_data in year_contracts.items():
                    if contract_data and isinstance(contract_data, dict):
                        try:
                            # التأكد من وجود UUID
                            if 'uuid' not in contract_data:
                                contract_data['uuid'] = str(uuid.uuid4())
                            
                            # إضافة metadata للنقل
                            contract_data['metadata'] = contract_data.get('metadata', {})
                            contract_data['metadata']['migrated_from'] = 'local_file'
                            contract_data['metadata']['migration_date'] = datetime.now().isoformat()
                            contract_data['metadata']['original_year'] = year
                            contract_data['metadata']['original_id'] = contract_id
                            
                            # إضافة إلى Firestore
                            doc_ref = db.collection('contracts').document(contract_data['uuid'])
                            doc_ref.set(contract_data)
                            
                            migrated_count += 1
                            logger.info(f"Migrated contract: {contract_data['uuid']}")
                            
                        except Exception as e:
                            logger.error(f"Error migrating contract {contract_id}: {e}")
        
        logger.info(f"Migration completed. {migrated_count} contracts migrated.")
        return True
        
    except Exception as e:
        logger.error(f"Error during migration: {e}")
        return False

def list_firebase_contracts():
    """عرض قائمة العقود في Firebase"""
    try:
        from firebase_admin_config import get_firestore_client
        
        db = get_firestore_client()
        if not db:
            logger.error("Failed to get Firestore client")
            return
        
        # جلب جميع العقود
        contracts_ref = db.collection('contracts')
        docs = contracts_ref.stream()
        
        contracts = []
        for doc in docs:
            contract_data = doc.to_dict()
            contracts.append({
                'uuid': contract_data.get('uuid'),
                'title': contract_data.get('title'),
                'client_name': contract_data.get('client_name'),
                'status': contract_data.get('status'),
                'created_at': contract_data.get('created_at')
            })
        
        if contracts:
            print(f"\n📋 العقود في Firebase ({len(contracts)} عقد):")
            print("-" * 80)
            for contract in contracts:
                print(f"UUID: {contract['uuid']}")
                print(f"العنوان: {contract['title']}")
                print(f"العميل: {contract['client_name']}")
                print(f"الحالة: {contract['status']}")
                print(f"تاريخ الإنشاء: {contract['created_at']}")
                print("-" * 40)
        else:
            print("\n📋 لا توجد عقود في Firebase")
        
    except Exception as e:
        logger.error(f"Error listing contracts: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔥 إدارة العقود في Firebase")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("الاستخدام:")
        print("  python add_contract_to_firebase.py add      # إضافة عقد تجريبي")
        print("  python add_contract_to_firebase.py migrate  # نقل العقود المحلية")
        print("  python add_contract_to_firebase.py list     # عرض العقود")
        return
    
    command = sys.argv[1].lower()
    
    if command == "add":
        print("📝 إضافة عقد تجريبي...")
        contract = create_sample_contract()
        
        if add_contract_to_firestore(contract):
            print(f"✅ تم إضافة العقد بنجاح!")
            print(f"UUID: {contract['uuid']}")
            print(f"رابط العرض: http://localhost:21226/contract/{contract['uuid']}")
        else:
            print("❌ فشل في إضافة العقد")
    
    elif command == "migrate":
        print("🔄 نقل العقود من الملف المحلي...")
        if migrate_local_contracts():
            print("✅ تم نقل العقود بنجاح!")
        else:
            print("❌ فشل في نقل العقود")
    
    elif command == "list":
        print("📋 عرض العقود في Firebase...")
        list_firebase_contracts()
    
    else:
        print(f"❌ أمر غير معروف: {command}")

if __name__ == "__main__":
    main()
