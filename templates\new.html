<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الملف</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fa;
            margin: 0;
            padding: 0;
            direction: rtl;
        }

        .main-content {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 70px);
            padding: 20px;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 80%;
            max-width: 800px;
        }
        h1 {
            color: #333;
            text-align: center;
            font-size: 1.5rem;
            margin-bottom: 20px;
        }
        label {
            font-size: 1rem;
            color: #555;
            margin-bottom: 8px;
            display: inline-block;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #ccc;
            font-size: 1rem;
            background-color: #f9f9f9;
            resize: none;
            margin-bottom: 20px;
        }
        textarea:focus {
            border-color: #007bff;
            outline: none;
            background-color: #fff;
        }
        button {
            background-color: #007bff;
            color: #fff;
            font-size: 1rem;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            color: green;
            font-size: 1.1rem;
            text-align: center;
            margin-top: 20px;
        }

        .error-message {
            color: red;
            font-size: 1.1rem;
            text-align: center;
            margin-top: 20px;
        }

        .link-button {
            background-color: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }

        .link-button:hover {
            background-color: #5a6268;
        }
    </style>
    <script>
        let updateInterval = null;
        let lastActivity = Date.now();
        let isPageVisible = true;

        // تحديث الرقم التسلسلي مع تحسينات الأداء
        function updateCurrentSerial() {
            // تجاهل التحديث إذا كانت الصفحة غير مرئية أو غير نشطة
            if (!isPageVisible || (Date.now() - lastActivity > 300000)) { // 5 دقائق عدم نشاط
                console.log('تم تجاهل التحديث - الصفحة غير نشطة');
                return;
            }

            fetch('/api/current_serial')
                .then(response => response.json())
                .then(data => {
                    if (data.current_serial !== undefined) {
                        document.getElementById('current-serial').textContent = data.current_serial;
                        // تحديث قيمة النموذج أيضاً
                        const textarea = document.getElementById('file_content');
                        if (textarea && textarea.value !== data.current_serial.toString()) {
                            // تحديث فقط إذا لم يكن المستخدم يكتب
                            if (document.activeElement !== textarea) {
                                textarea.value = data.current_serial;
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('خطأ في جلب الرقم التسلسلي:', error);
                });
        }

        // مراقبة نشاط المستخدم
        function trackUserActivity() {
            lastActivity = Date.now();
        }

        // مراقبة رؤية الصفحة
        function handleVisibilityChange() {
            isPageVisible = !document.hidden;
            if (isPageVisible) {
                lastActivity = Date.now();
                updateCurrentSerial(); // تحديث فوري عند العودة للصفحة
            }
        }

        // بدء التحديث التلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث فوري
            updateCurrentSerial();

            // تحديث كل 30 ثانية بدلاً من 5 ثوانٍ لتوفير الموارد
            updateInterval = setInterval(updateCurrentSerial, 30000);

            // مراقبة نشاط المستخدم
            ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(event => {
                document.addEventListener(event, trackUserActivity, true);
            });

            // مراقبة رؤية الصفحة
            document.addEventListener('visibilitychange', handleVisibilityChange);
        });

        // تنظيف عند مغادرة الصفحة
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    {% include 'toolbar.html' %}

<div class="main-content">
    <div class="container">
    <h1>تعديل الرقم التسلسلي</h1>

    <!-- عرض الرقم التسلسلي الحالي -->
    <div style="background-color: #e6f3ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center; border: 2px solid #007bff;">
        <h2 style="color: #007bff; margin: 0;">الرقم التسلسلي الحالي</h2>
        <div id="current-serial" style="font-size: 2em; font-weight: bold; color: #d00; margin: 10px 0;">{{ current_serial }}</div>
        <small style="color: #666;">يتم التحديث كل 30 ثانية (يتوقف عند عدم النشاط)</small>
    </div>

    <form action="/" method="get">
        <button class="link-button" type="submit">الرجوع الى الرئيسية</button>
    </form>

    <form method="POST">
        <label for="file_content">الرقم التسلسلي الجديد:</label>
        <textarea id="file_content" name="file_content" rows="3" placeholder="أدخل الرقم التسلسلي الجديد">{{ file_content }}</textarea><br>
        <button type="submit">حفظ التعديلات</button>
    </form>

    {% if message %}
        {% if 'خطأ' in message %}
        <p class="error-message">{{ message }}</p>
        {% else %}
        <p class="message">{{ message }}</p>
        {% endif %}
    {% endif %}
    </div>
</div>

</body>
</html>
