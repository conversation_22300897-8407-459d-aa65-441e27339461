@echo off
chcp 65001 > nul
echo ========================================
echo        تشغيل سيرفر QR للعقود
echo ========================================
echo.

cd /d "%~dp0"

echo جاري التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo جاري التحقق من Flask...
python -c "import flask" > nul 2>&1
if errorlevel 1 (
    echo جاري تثبيت Flask...
    pip install flask
    if errorlevel 1 (
        echo خطأ في تثبيت Flask
        pause
        exit /b 1
    )
)

echo.
echo جاري تشغيل السيرفر...
echo للوصول للسيرفر: http://localhost:21226
echo لإيقاف السيرفر: اضغط Ctrl+C
echo.

python run_simple.py

pause
