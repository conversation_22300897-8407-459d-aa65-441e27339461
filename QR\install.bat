@echo off
chcp 65001 >nul

echo 🚀 تثبيت QR Contract Viewer
echo ==========================

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

REM إنشاء البيئة الافتراضية
echo 📦 إنشاء البيئة الافتراضية...
python -m venv venv

REM تفعيل البيئة الافتراضية
echo 🔧 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

REM ترقية pip
echo ⬆️ ترقية pip...
python -m pip install --upgrade pip

REM تثبيت المتطلبات
echo 📚 تثبيت المتطلبات...
pip install -r requirements.txt

REM نسخ ملف البيئة
if not exist .env (
    echo 📝 إنشاء ملف .env...
    copy .env.example .env
    echo ⚠️ يرجى تعديل ملف .env بالقيم الصحيحة لـ Firebase
) else (
    echo ✅ ملف .env موجود بالفعل
)

echo.
echo 🎉 تم التثبيت بنجاح!
echo.
echo الخطوات التالية:
echo 1. تعديل ملف .env بقيم Firebase الصحيحة
echo 2. تشغيل التطبيق: python run.py
echo.
echo للمساعدة، راجع ملف README.md
pause
