<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج تعبئة العقد</title>
    <style>
        body {
            font-family: 'Traditional Arabic', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #fff;
            direction: rtl;
        }

        .main-content {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: calc(100vh - 70px);
            padding: 20px;
        }
        .container {
            background: url('background.jpg') no-repeat center top;
            background-size: cover;
            padding: 40px;
            border: 2px solid #000;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            max-width: 800px;
            width: 100%;
            text-align: right;
        }
        h1 {
            text-align: center;
            color: #d00;
            font-size: 36px;
        }
        form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        label {
            font-weight: bold;
            color: #000;
        }
        input, select, button, a {
            padding: 10px;
            border: 1px solid #000;
            border-radius: 5px;
            font-size: 18px;
        }
        button, a {
            background: #d00;
            color: white;
            border: none;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        button:hover, a:hover {
            background: #b00;
        }
        .photo-box {
            border: 1px solid #000;
            width: 100%;
            height: 250px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            position: relative;
        }
        .photo-box img {
            max-width: 100%;
            max-height: 100%;
        }
        video {
            width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    {% include 'toolbar.html' %}

    <div class="main-content">
        <div class="container">
        <h1>نموذج تعبئة عقد بيع وشراء السيارات</h1>
        <form action="/" method="POST">
            <label for="name_1">اسم المالك الشرعي:</label>
            <input type="text" name="name_1" id="name_1" required>

            <label for="location_1">عنوان المالك الشرعي:</label>
            <input type="text" name="location_1" id="location_1" required>

            <label for="name_2">اسم البائع:</label>
            <input type="text" name="name_2" id="name_2" required>

            <label for="location_2">عنوان البائع:</label>
            <input type="text" name="location_2" id="location_2" required>

            <label for="id_1">رقم الهوية للبائع:</label>
            <input type="text" name="id_1" id="id_1" required>

            <label for="phone_1">رقم الهاتف للبائع:</label>
            <input type="text" name="phone_1" id="phone_1" required>

            <label for="name_3">اسم المشتري:</label>
            <input type="text" name="name_3" id="name_3" required>

            <label for="location_3">عنوان المشتري:</label>
            <input type="text" name="location_3" id="location_3" required>

            <label for="id_2">رقم هوية المشتري:</label>
            <input type="text" name="id_2" id="id_2" required>

            <label for="phone_2">رقم الهاتف للمشتري:</label>
            <input type="text" name="phone_2" id="phone_2" required>

            <label for="car_num">رقم السيارة:</label>
            <input type="text" name="car_num" id="car_num" required>

            <label for="sin_num">رقم السنوية:</label>
            <input type="text" name="sin_num" id="sin_num" required>

            <label for="car_prop">نوع السيارة:</label>
            <select name="car_prop" id="car_prop" required>
                <option value="خصوصي">خصوصي</option>
                <option value="اجرة">اجرة</option>
                <option value="حمل">حمل</option>
            </select>

            <label for="car_type">نوع السيارة:</label>
            <input type="text" name="car_type" id="car_type" required>

            <label for="car_model">موديل السيارة:</label>
            <input type="text" name="car_model" id="car_model" required>

            <label for="car_colar">لون السيارة:</label>
            <input type="text" name="car_colar" id="car_colar" required>

            <label for="engen_num">رقم المحرك:</label>
            <input type="text" name="engen_num" id="engen_num" required>

            <label for="sasi_num">رقم الشاصي:</label>
            <input type="text" name="sasi_num" id="sasi_num" required>

            <label for="badal_num">قيمة البيع:</label>
            <input type="text" name="badal_num" id="badal_num" required>

            <label for="mony_num">المبلغ المقبوض:</label>
            <input type="text" name="mony_num" id="mony_num" required>

            <label for="mony_not_delevired">المبلغ المتبقي:</label>
            <input type="text" name="mony_not_delevired" id="mony_not_delevired" required>

            <label for="time">الوقت :</label>
            <input type="text" name="time" id="time" required>

            <label for="day">اليوم:</label>
            <select name="day" id="day" required>
                <option value="السبت">السبت</option>
                <option value="الاحد">الاحد</option>
                <option value="الاثنين">الاثنين</option>
                <option value="الثلاثاء">الثلاثاء</option>
                <option value="الاربعاء">الاربعاء</option>
                <option value="الخميس">الخميس</option>
                <option value="الجمعة">الجمعة</option>
            </select>

            <label for="note_a">ملاحظة أ:</label>
            <input type="text" name="note_a" id="note_a">

            <label for="note_b">ملاحظة ب:</label>
            <input type="text" name="note_b" id="note_b">

            <label for="note_c">ملاحظة ج:</label>
            <input type="text" name="note_c" id="note_c">

            <!-- نافذة لعرض الكاميرا الحية -->
            <h2>معاينة صورة البائع</h2>
            <div class="photo-box" id="photo-box-buyer">
                <p>لم يتم التقاط صورة بعد.</p>
            </div>
            <video id="video-buyer" autoplay></video>
            <button type="button" onclick="capturePhoto('buyer')">التقاط صورة البائع</button>

            <!-- نافذة لعرض الكاميرا الحية للمشتري -->
            <h2>معاينة صورة المشتري</h2>
            <div class="photo-box" id="photo-box-seller">
                <p>لم يتم التقاط صورة بعد.</p>
            </div>
            <video id="video-seller" autoplay></video>
            <button type="button" onclick="capturePhoto('seller')">التقاط صورة المشتري</button>

            <button type="submit">ارسال البيانات</button>
        </form>
    </div>

    <script>
    let buyerPhoto = null;
    let sellerPhoto = null;
    let buyerStream = null;
    let sellerStream = null;

    // بدء الفيديو للمشتري أو البائع
    function startVideo(type) {
        if (type === 'buyer') {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(function (stream) {
                    buyerStream = stream;
                    const video = document.getElementById('video-buyer');
                    video.srcObject = stream;
                })
                .catch(function (err) {
                    alert("حدث خطأ في الوصول إلى الكاميرا: " + err);
                });
        }

        if (type === 'seller') {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(function (stream) {
                    sellerStream = stream;
                    const video = document.getElementById('video-seller');
                    video.srcObject = stream;
                })
                .catch(function (err) {
                    alert("حدث خطأ في الوصول إلى الكاميرا: " + err);
                });
        }
    }

    // التقاط صورة من الفيديو للمشتري أو البائع
    function capturePhoto(type) {
        let videoElement;
        let canvas = document.createElement('canvas');
        let ctx = canvas.getContext('2d');

        if (type === 'buyer') {
            videoElement = document.getElementById('video-buyer');
        } else if (type === 'seller') {
            videoElement = document.getElementById('video-seller');
        }

        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

        if (type === 'buyer') {
            buyerPhoto = canvas.toDataURL('image/jpeg');
            document.getElementById('photo-box-buyer').innerHTML = `<img src="${buyerPhoto}" alt="Buyer Photo">`;
        } else if (type === 'seller') {
            sellerPhoto = canvas.toDataURL('image/jpeg');
            document.getElementById('photo-box-seller').innerHTML = `<img src="${sellerPhoto}" alt="Seller Photo">`;
        }

        // إيقاف الفيديو بعد التقاط الصورة
        if (type === 'buyer') {
            buyerStream.getTracks().forEach(track => track.stop());
        } else if (type === 'seller') {
            sellerStream.getTracks().forEach(track => track.stop());
        }
    }

    // التحقق من الحقول والتأكد من ملئها قبل إرسال البيانات
    document.getElementById("contract-form").addEventListener("submit", function(event) {
        event.preventDefault();

        const formElements = event.target.elements;
        let formValid = true;

        // التحقق من أن جميع الحقول قد تم تعبئتها
        for (let i = 0; i < formElements.length; i++) {
            if (formElements[i].type !== "submit" && !formElements[i].value && formElements[i].name !== "buyerPhoto" && formElements[i].name !== "sellerPhoto") {
                formValid = false;
                break;
            }
        }

        // التحقق من التقاط صور المشتري والبائع
        if (!buyerPhoto) {
            alert("يجب التقاط صورة للبائع.");
            formValid = false;
        }
        if (!sellerPhoto) {
            alert("يجب التقاط صورة للمشتري.");
            formValid = false;
        }

        if (!formValid) {
            return; // إذا كان هناك خطأ، لا نرسل البيانات
        }

        // إعداد FormData مع الحقول والصور
        const formData = new FormData();
        for (let i = 0; i < formElements.length; i++) {
            const element = formElements[i];
            if (element.type !== "submit" && element.name !== "buyerPhoto" && element.name !== "sellerPhoto") {
                formData.append(element.name, element.value);
            }
        }

        // إضافة الصور إلى FormData
        if (buyerPhoto) {
            formData.append('buyerPhoto', dataURLtoBlob(buyerPhoto), 'buyer_photo.jpg');
        }
        if (sellerPhoto) {
            formData.append('sellerPhoto', dataURLtoBlob(sellerPhoto), 'seller_photo.jpg');
        }

        // إرسال البيانات إلى الخادم
        fetch('/submit-data', {
            method: 'POST',
            body: formData
        })
        .then(response => response.blob())
        .then(blob => {
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'filled_template.docx';
            link.click();
        })
        .catch(error => {
            console.error('Error:', error);
            alert("حدث خطأ أثناء إرسال البيانات.");
        });
    });

    // تحويل DataURL إلى Blob
    function dataURLtoBlob(dataURL) {
        const byteString = atob(dataURL.split(',')[1]);
        const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0];
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
        }
        return new Blob([ab], { type: mimeString });
    }

    // تحميل الفيديو للمشتري والبائع عند تحميل الصفحة
    window.onload = function() {
        startVideo('buyer');
        startVideo('seller');
    };
</script>
        </div>
    </div>

</body>
</html>
